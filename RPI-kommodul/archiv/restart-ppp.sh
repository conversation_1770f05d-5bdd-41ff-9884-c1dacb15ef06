#!/bin/bash

# hál<PERSON><PERSON>t <PERSON>, nehogy a szolg<PERSON><PERSON><PERSON><PERSON> bontson szét
# -------------------------------------------------------

/usr/bin/logger "GSM kapcsolat tervezett megújítás..."
/sbin/ifdown --force ppp0
/usr/bin/logger "ppp0 interface kilőve"
/bin/sleep 3

CHECKPPPD=$(/usr/bin/pgrep pppd)
if [ -z "$CHECKPPPD" ]
then
  /usr/bin/logger "ppdd rendben leállt OK"
else
  /usr/bin/logger "pppd be van ragadva -> kill neki"
  /usr/bin/killall pppd
fi
/bin/sleep 2

/usr/bin/logger "ppp0 interface-t újra felhúzzuk"
/sbin/ifup ppp0
