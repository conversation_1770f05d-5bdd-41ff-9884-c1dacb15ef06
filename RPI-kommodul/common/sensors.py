#!/usr/bin/python3
# -*- coding: utf-8 -*-

import time
import bme280
import smbus2
import collections
import board
import busio
import adafruit_ads1x15.ads1115 as ads
from adafruit_ads1x15.ads1x15 import Mode
from adafruit_ads1x15.analog_in import AnalogIn

from . import funcs


#########################################################
#                                                       #
#   H<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON> és légnyomás funkciók     #
#                                                       #
#########################################################
@funcs.add_cls_logger
class THPMETER:

    PORT = 1
    ADDRESS = 0x77      # Adafruit BME280 address. checked with:  i2cdetect -y 1

    def __enter__(self):
        try:
            self._bus = smbus2.SMBus(self.PORT)      # Rev 3 Pi uses 1
            self._calparam = bme280.load_calibration_params(self._bus, self.ADDRESS)
        except Exception as e:
            self.__log.warning('Hőmérő nem működik: ' + repr(e))
        return self

    def __exit__(self, errortype, value, traceback):
        if self._bus:
            try:
                self._bus.close()
            except Exception as e:
                self.__log.warning('Hőmérő nem zárható be: ' + repr(e))

    # ----------------------------
    # | levegőkörnyezeti adatok  |
    # ----------------------------
    def getData(self):
        if not self._bus:
            return None
        Bd = collections.namedtuple('compensated_readings', ['temperature', 'humidity', 'pressure'])
        bme280_data = Bd(0, 0, 0)
        try:
            bme280_data = bme280.sample(self._bus, self.ADDRESS, self._calparam)
        except Exception as e:
            self.__log.warning('Hőmérő nem működik: ' + repr(e))
        finally:
            return bme280_data.temperature, bme280_data.humidity, bme280_data.pressure


#########################################################
#                                                       #
#   Fényerősség mérés, doboznyitás érzékeléshez         #
#                                                       #
#########################################################
@funcs.add_cls_logger
class LUXMETER:

    # Define some constants from the datasheet
    POWER_DOWN = 0x00   # No active state
    POWER_ON = 0x01     # Power on
    RESET = 0x07        # Reset data register value
    ONE_TIME_HIGH_RES_MODE = 0x20

    PORT = 1
    ADDRESS = 0x23      # .............. address. checked with:  i2cdetect -y 1

    def __enter__(self):
        try:
            self._bus = smbus2.SMBus(self.PORT)      # Rev 3 Pi uses 1
        except Exception as e:
            self.__log.warning('Fénymérő nem működik: ' + repr(e))
        return self

    def __exit__(self, errortype, value, traceback):
        if self._bus:
            try:
                self._bus.close()
            except Exception as e:
                self.__log.warning('Fénymérő nem zárható be: ' + repr(e))

    # -----------------------
    # | fényerősség adatok  |
    # -----------------------
    def getData(self):
        if not self._bus:
            return None
        data = 0
        try:
            data = self._bus.read_word_data(self.ADDRESS, self.ONE_TIME_HIGH_RES_MODE)
        except Exception as e:
            self.__log.warning('Fénymérő nem működik: ' + repr(e))
        finally:
            return int((data >> 8 | (data & 0xff) << 8) / 1.2)


#########################################################
#                                                       #
#   térbeli irányultság mérés, matatás érzékeléshez     #
#                                                       #
#########################################################
@funcs.add_cls_logger
class ACCELMETER:

    PORT = 1
    ADDRESS = 0x53      # ADXL-345 address. checked with:  i2cdetect -y 1

    def __enter__(self):
        try:
            self._bus = smbus2.SMBus(self.PORT)      # Rev 3 Pi uses 1
            # ADXL345 address, 0x53(83)
            # Select bandwidth rate register, 0x2C(44)
            #       0x0A(10)    Normal mode, Output data rate = 100 Hz
            self._bus.write_byte_data(self.ADDRESS, 0x2C, 0x0A)
            # ADXL345 address, 0x53(83)
            # Select power control register, 0x2D(45)
            #       0x08(08)    Auto Sleep disable
            self._bus.write_byte_data(self.ADDRESS, 0x2D, 0x08)
            # ADXL345 address, 0x53(83)
            # Select data format register, 0x31(49)
            #       0x08(08)    Self test disabled, 4-wire interface
            #                   Full resolution, Range = +/-2g
            self._bus.write_byte_data(self.ADDRESS, 0x31, 0x08)
        except Exception as e:
            self.__log.warning('Dőlésmérő nem működik: ' + repr(e))
        return self

    def __exit__(self, errortype, value, traceback):
        if self._bus:
            self._bus.close()

    def _read_reg(self, r0, r1):
        if not self._bus:
            return None
        data0 = 0
        data1 = 1
        try:
            # Read data back from registers, 2 bytes
            # ?-Axis LSB, ?-Axis MSB
            data0 = self._bus.read_byte_data(self.ADDRESS, r0)
            data1 = self._bus.read_byte_data(self.ADDRESS, r1)
        except Exception as e:
            self.__log.warning('Dőlésmérő nem olvasható: ' + repr(e))
        finally:
            # Convert the data to 10-bits
            accl = ((data1 & 0x03) * 256) + data0
            if accl > 511:
                accl -= 1024
            return accl * 0.00390625    # g-ben kapjuk a gyorsulás eredményt

    def xAccel(self):
        return self._read_reg(0x32, 0x33)

    def yAccel(self):
        return self._read_reg(0x34, 0x35)

    def zAccel(self):
        return self._read_reg(0x36, 0x37)

    # -----------------------
    # | irányultság adatok  |
    # -----------------------
    def getData(self):
        return self.xAccel(), self.yAccel(), self.zAccel()


#########################################################
#                                                       #
#           SOLAR UPS akksi feszültség monitoring       #
#                                                       #
#########################################################
@funcs.add_cls_logger
class BATMON:

    ADPIN = ads.P3          # analog-digital konverter melyik pinjére jön a jel
    VDIV = 12.61 / 2.21     # kézi mérés alapján: feszültségosztó értékének hányszorosa a valós fesz.
    UPSVLIMIT = 11.3        # ha napelemről megyünk, ennél biztosan nem magasabb a mérhető fesz.

    def __init__(self):
        try:
            i2c = busio.I2C(board.SCL, board.SDA)
            # Create the ADC object using the I2C bus
            myads = ads.ADS1115(i2c, gain=1, mode=Mode.SINGLE)
            # Create single-ended input on channel 0
            self._chan = AnalogIn(myads, self.ADPIN)
            # fesz. változás miatt kiegyenlített adatsort átlagolunk majd
            self._last30volts = []
        except Exception as e:
            self.__log.warning('Akksi monitoring nem működik: ' + repr(e))

    def readCurrentVoltage(self):
        # már eleve többször olvasunk, hogy a fesz. ingadozást valamennyire kiegyenlítsük
        v = list()
        try:
            for _ in range(4):
                v.append(self._chan.voltage)
                time.sleep(0.1)
        except Exception as e:
            self.__log.warning('Akksi feszültség nem olvasható ki: ' + repr(e))
            v.append(-1)
        if len(self._last30volts) >= 30:
            self._last30volts.pop(0)
        self._last30volts.append(sum(v) / len(v) * self.VDIV)

    def avgVoltage(self):
        return sum(self._last30volts) / len(self._last30volts)

    def avgLast5minVoltage(self):
        last5 = self._last30volts[:-6:-1]
        return sum(last5) / len(last5)

    def getVoltage(self):
        # az utolsó 30 mérés átlagát adjuk, hogy tovább egyenlítsük az ingadozást
        self.readCurrentVoltage()
        return self.avgVoltage()

    def poweredByGrid(self):
        # felső szélsőértékek alapján lehet megmondani, hogy elemről megyünk-e
        return 1 if self.avgLast5minVoltage() > self.UPSVLIMIT else 0


# csak néhány egyszerű teszt
if __name__ == "__main__":

    with THPMETER() as thp:
        print('{0[0]:.1f}°C  {0[1]:.1f}rh%  {0[2]:.1f}hPa'.format(thp.getData()))
    print()

    with LUXMETER() as lux:
        print('{} lux'.format(lux.getData()))
    print()

    with ACCELMETER() as accel:
        xAccl, yAccl, zAccl = accel.getData()
        print("X-tengely gyorsulás: %f" % xAccl)
        print("Y-tengely gyorsulás: %f" % yAccl)
        print("Z-tengely gyorsulás: %f" % zAccl)
    print()
