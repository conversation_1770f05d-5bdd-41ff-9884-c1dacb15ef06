#!/usr/bin/python3
# -*- coding: utf-8 -*-

import serial
import time
import subprocess as subpr
from decimal import Decimal
import logging
from datetime import datetime
import Adafruit_SSD1306
from PIL import Image
from PIL import ImageFont
from PIL import ImageDraw

from . import funcs


####################################################
#                                                  #
#  SIM7000E hat: GSM és GPS eszköz közös funkciók  #
#     sajnos a chip nem tud egyszerre internet     #
#     és SMS funkciót kezelni, de még csak GPS-t   #
#     sem párhuzamosan, ezért a folyamatos ppp     #
#     kapcsolat a megoldás a távfelügyeletre       #
#                                                  #
####################################################
@funcs.add_cls_logger
class SIM7000:

    def __init__(self, mode, debug):
        # SIM7000 USB csatlakoztatásnál más-más porton elérhetőek az egyes funkciók
        if mode == 'GSM':
            self._serialPort = '/dev/ttyUSB2'
            self._modeGSM = True
        elif mode == 'GPS':
            self._serialPort = '/dev/ttyUSB3'
            self._modeGSM = False
        else:
            raise Exception('a használati mód csak "GSM" vagy "GPS" lehet')
        self._debug = debug
        if debug and not self.__log.hasHandlers():
            logging.basicConfig(
                filename='{}-{}.log'.format(mode, datetime.today().isoformat()),
                level=logging.DEBUG
            )

    def __enter__(self):
        self._port = serial.Serial(
            self._serialPort, baudrate=115200, timeout=30,
            bytesize=8, parity="N", stopbits=1,
            # USB-s csatlakozásnál hardware flow control kell
            rtscts=True, dsrdtr=True
        )
        return self

    def __exit__(self, errortype, value, traceback):
        if self._port:
            self._safeClose()
            self._port.close()

    # ----------------------------------------------------------
    # | biztonságosan kikapcsoljuk az eszközt vagy kapcsolatot |
    # ----------------------------------------------------------
    def _safeClose(self):
        if self._modeGSM:
            self._send('AT+SAPBR=0,1')   # GSM
        else:
            self._send('AT+CGNSPWR=0')   # GPS/GNSS

    # ------------------------------------
    # | AT parancs küldés a soros portra |
    # ------------------------------------
    def _send(self, cmd):
        self._port.reset_output_buffer()
        self._port.reset_input_buffer()
        # '\r\n' felel meg az ENTER billentyűnek
        self._port.write((cmd + '\r\n').encode())

    # -----------------------------------------------------------
    # | AT parancs kimenet olvasás egészen az elvárt eredményig |
    # -----------------------------------------------------------
    def _recieve(self, expectedString):
        # parancs echo
        if self._debug:
            self.__log.debug(self._port.readline().decode('utf-8').strip())
        # parancs eredmény
        result = ''
        while expectedString not in result:
            # több sornyi üres semmit igyekszünk egyszerűsíteni
            rcv = self._port.read(self._port.in_waiting).decode('utf-8').strip('').replace('\r\n', '.')
            if self._debug:
                self.__log.debug(rcv)
            result += rcv
            if 'ERROR' in rcv:
                self._safeClose()
                return False
        return True

    # ------------------------------------------------------
    # | AT parancs küldés és várakozás az elvárt kimenetre |
    # ------------------------------------------------------
    def _sendCommand(self, command, expectedResult):
        time.sleep(0.1)
        self._send(command)
        time.sleep(0.5)
        if not self._recieve(expectedResult):    # TODO: ennél jobb megoldás kell
            self._safeClose()
            return False
        return True


###################################################
#                                                 #
#           GSM adatküldés funkciók               #
#                                                 #
###################################################
class GSM(SIM7000):

    def __init__(self, debug=False):
        super().__init__('GSM', debug)

    # ------------------------
    # | HTTP POST adatküldés |
    # ------------------------
    def sendHttpPOST(self, url, jsonData):
        commandList = (
            {'AT': 'OK'},                                               # check init
            {'AT+SAPBR=3,1,"APN","internet.vodafone.net"': 'OK'},       # set apn
            {'AT+SAPBR=1,1': 'OK'},                                     # open connection
            {'AT+SAPBR=2,1': 'OK'},                                     # check connection
            {'AT+HTTPINIT': 'OK'},                                      # init HTTP service
            {'AT+HTTPPARA="CID",1': 'OK'},                              # set param and URL for session
            {'AT+HTTPPARA="URL","{}"'.format(url): 'OK'},
            {'AT+HTTPPARA="CONTENT","application/json"': 'OK'},
            {'AT+HTTPDATA={},10000'.format(len(jsonData) + 2): 'DOWNLOAD'},
            {jsonData: 'OK'},
            {'AT+HTTPACTION=1': '+HTTPACTION'},                         # POST session start
            {'AT+HTTPSTATUS?': '+HTTPSTATUS'},                          # check for result of POST query
            {'AT+HTTPREAD': 'OK'},                                      # read any reply
            {'AT+HTTPTERM': 'OK'},                                      # terminate HTTP service
        )
        resultOK = True
        for cmdresult in commandList:
            for cmd, result in cmdresult.items():
                if not self._sendCommand(cmd, expectedResult=result):
                    resultOK = False
            if not resultOK:
                break
        return resultOK

    # -----------------------------------------------------------------------
    # | FTP PUT adatküldés, egyelőre csak kb. 1360 bájtméret alatti adathoz |
    # -----------------------------------------------------------------------
    def sendFtpPUT(self, ftpServer, ftpLogin, ftpPwd, jsonData):
        commandList = (
            {'AT': 'OK'},                                               # check init
            {'AT+SAPBR=3,1,"APN","internet.vodafone.net"': 'OK'},       # set apn
            {'AT+SAPBR=1,1': 'OK'},                                     # open connection
            {'AT+SAPBR=2,1': 'OK'},                                     # check connection
            {'AT+FTPCID=1': 'OK'},                                      # set param for FTP session
            {'AT+FTPSERV="{}"'.format(ftpServer): 'OK'},
            {'AT+FTPUN="{}"'.format(ftpLogin): 'OK'},
            {'AT+FTPPW="{}"'.format(ftpPwd): 'OK'},
            {'AT+FTPPUTNAME="/home/<USER>/proba.json"': 'OK'},
            {'AT+FTPPUT=1': '+FTPPUT'},                                 # FTP session start
            {'AT+FTPPUT=2,{}'.format(len(jsonData)): '+FTPPUT'},
            {jsonData: 'OK'},
            {'AT+FTPPUT=2,0': '+FTPPUT'},                               # terminate FTP session
        )
        resultOK = True
        for cmdresult in commandList:
            for cmd, result in cmdresult.items():
                if not self._sendCommand(cmd, expectedResult=result):
                    resultOK = False
            if not resultOK:
                break
        return resultOK


#########################################################
#                                                       #
#                     GPS funkciók                      #
#                                                       #
#########################################################
@funcs.add_cls_logger
class GPS(SIM7000):

    def __init__(self, debug=False):
        super().__init__('GPS', debug)

    # ---------------------------
    # | GPS pozíció lekérdezése |
    # ---------------------------
    def getPosition(self):
        # to power on the GPS
        self._sendCommand('AT+CGNSPWR=1', expectedResult='OK')
        # get NMEA data to same AT port
        self._sendCommand('AT+CGNSTST=1', expectedResult='OK')
        # get GPS information
        self._send('AT+CGNSINF')
        rcv = self._port.read(self._port.in_waiting).decode('ascii').strip()
        if self._debug:
            self.__log.debug(rcv)
        time.sleep(.1)
        ck = 5      # ennyi adatból átlagolunk majd
        lats = list()
        lngs = list()
        while ck > 0:
            fd = self._port.read(self._port.in_waiting).decode('ascii').strip()
            time.sleep(.5)
            if '$GNRMC' in fd:        # To Extract Lattitude and
                if self._debug:
                    self.__log.debug(fd)
                ps = fd.find('$GNRMC')        # Longitude
                dif = len(fd) - ps
                if dif > 50:
                    data = fd[ps:(ps + 50)]
                    ds = data.find('A')        # Check GPS is valid
                    if 0 < ds < 20:
                        p = [pos for pos, ch in enumerate(data) if ch == ',']
                        lat = data[(p[2] + 1):p[3]]
                        lon = data[(p[4] + 1):p[5]]
                        # GPS data calculation
                        s1 = lat[2:len(lat)]
                        s1 = Decimal(s1)
                        s1 = s1 / 60
                        s11 = int(lat[0:2])
                        s1 = s11 + s1
                        s2 = lon[3:len(lon)]
                        s2 = Decimal(s2)
                        s2 = s2 / 60
                        s22 = int(lon[0:3])
                        s2 = s22 + s2
                        lats.append(s1)
                        lngs.append(s2)
                        ck -= 1     # már csak eggyel kevesebb adat kell
        lat = sum(lats) / len(lats)
        lng = sum(lngs) / len(lngs)
        if self._debug:
            self.__log.debug('latitude: {}  longitude: {}'.format(lat, lng))
        # to power off the GPS
        self._sendCommand('AT+CGNSPWR=0', expectedResult='OK')
        return lat, lng


##########################################################
#                                                        #
#                     SMS funkciók,                      #
# a működéshez a gnokii-cli be kell legyen konfigurálva  #
#                                                        #
##########################################################
@funcs.add_cls_logger
class SMS:

    # --------------------------------------------------------------
    # | eszköz gyártó, azonosító és firmware verziószám lekérdezés |
    # --------------------------------------------------------------
    @classmethod
    def identify(cls):
        with subpr.Popen(['gnokii', '--identify'], stdout=subpr.PIPE, stderr=subpr.STDOUT) as myid:
            for aline in myid.stdout:
                line = aline.decode().strip()
                if 'GNOKII Version' in line or 'flags section' in line:
                    continue
                cls.__log.debug(line)

    # --------------
    # | SMS küldés |
    # --------------
    @classmethod
    def send(cls, text: str, phoneNum: str = '06703177131'):
        cmd = ['gnokii', '--sendsms', '{}'.format(phoneNum)]
        result = False
        p = subpr.run(cmd, input=text.encode(), stdout=subpr.PIPE, stderr=subpr.STDOUT)
        for aline in p.stdout.decode().split('\n'):
            line = aline.strip()
            if 'GNOKII Version' in line:
                continue
            cls.__log.debug(line)
            if 'Send succeeded' in line:
                result = True
        return result

    # ------------------------------------------------------------------------
    # | beérkezett SMS ellenőrzés, ha van olvasatlan, akkor True az eredmény |
    # ------------------------------------------------------------------------
    @classmethod
    def hasIncoming(cls):
        hasUnread = False
        with subpr.Popen(['gnokii', '--showsmsfolderstatus'], stdout=subpr.PIPE, stderr=subpr.STDOUT) as mysms:
            for aline in mysms.stdout:
                line = aline.decode().strip()
                cls.__log.debug(line)
                if 'SIM card' not in line:
                    continue
                tokens = line.split()
                if int(tokens[4]) > 0:
                    hasUnread = True
        return hasUnread

    # -----------------------------------
    # | SMS fogadás (ha van olvasatlan) |
    # -----------------------------------
    @classmethod
    def get(cls):
        cmd = 'gnokii --getsms SM 0'
        with subpr.Popen(cmd, stdout=subpr.PIPE, stderr=subpr.STDOUT) as mysms:
            for aline in mysms.stdout:
                line = aline.decode().strip()
                if 'GNOKII Version' in line:
                    continue
                cls.__log.debug(line)


###############################
#                             #
#   OLED képernyő kezelés     #
#                             #
###############################
@funcs.add_cls_logger
class OLED:

    # Raspberry Pi pin configuration:
    RST = 24

    def __init__(self, fontsize=18):
        self._fsize = fontsize
        self._font = ImageFont.truetype('/usr/share/fonts/truetype/freefont/FreeSans.ttf', self._fsize)
        self._image = None
        self._draw = None
        try:
            # 128x32 display with hardware I2C:
            self._disp = Adafruit_SSD1306.SSD1306_128_32(rst=self.RST)
            self._disp.begin()
        except Exception as e:
            self.__log.warning('OLED képernyő nem működik: ' + repr(e))

    def switchOn(self):
        if self._disp:
            try:
                self._disp.command(Adafruit_SSD1306.SSD1306_DISPLAYON)
                self._image = Image.new('1', (self._disp.width, self._disp.height))
                self._draw = ImageDraw.Draw(self._image)
            except Exception as e:
                self.__log.warning('OLED képernyő nem kapcsolható be: ' + repr(e))

    def switchOff(self):
        if self._disp:
            try:
                self._disp.command(Adafruit_SSD1306.SSD1306_DISPLAYOFF)
            except Exception as e:
                self.__log.warning('OLED képernyő nem kapcsolható ki: ' + repr(e))

    def show(self, x, y, text):
        if self._disp:
            self._draw.rectangle((0, 0, self._disp.width, self._disp.height), outline=0, fill=0)
            self._draw.text((x, y), text, font=self._font, fill=255)
            try:
                self._disp.image(self._image)
                self._disp.display()
            except Exception as e:
                self.__log.warning('OLED képernyő nem írható: ' + repr(e))


# csak néhány egyszerű teszt
if __name__ == "__main__":

    print()

    # SMS-nél statikus hívások
    SMS.identify()
    SMS.hasIncoming()
    print()

    # a többinél context manager van
    with GSM(debug=True) as s7gsm:
        print(
            'SEND OK' if s7gsm.sendHttpPOST(
                'http://www.imagmernok.hu/on-line/ites-kvmod-test.php', '{"kiaz":"envagyok","mivan":"sokvalami"}'
            ) else 'HTTP ERROR!'
        )
        # s7gsm.sendFtpPUT('81.93.193.15', 'itesuser', 'p1Mb(q7jpZ.qM/<P', '{"csakproba": "adat"}\n')

    with GPS() as s7gps:
        s7gps.getPosition()

    print('30 s-ig működik a képernyő', end='', flush=True)
    display = OLED()
    display.switchOn()
    display.show(5, 5, 'Hajrá!')
    for i in range(30):
        print('.', end='', flush=True)
        time.sleep(1)
    display.switchOff()
    print()
