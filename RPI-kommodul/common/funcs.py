#!/usr/bin/python3
# -*- coding: utf-8 -*-

import logging
import psutil
import socket
import math
from subprocess import Popen, PIPE
from typing import Optional


# alap függvények
# ----------------

# segédfügg<PERSON><PERSON><PERSON> h<PERSON><PERSON> el<PERSON>őrzéshez
def interface_up(interface) -> bool:
    interface_addrs = psutil.net_if_addrs().get(interface) or []
    return socket.AF_INET in [snicaddr.family for snicaddr in interface_addrs]


# segédfüggv<PERSON><PERSON>, csak IPv4 cím lekéréshez
def interface_getIP(interface) -> Optional[str]:
    if_addrs = psutil.net_if_addrs().get(interface) or []
    if len(if_addrs):
        for a in if_addrs:
            if a[0].name == 'AF_INET':
                return a.address
    return None


# logaritmikus átlagolás
def log10_avg(x1: float, x2: float) -> float:
    return 10 * math.log10(
        (math.pow(10, x1 / 10) + math.pow(10, x2 / 10)) / 2.0
    )


# class logolás<PERSON>z dekorátor
def add_cls_logger(cls: type) -> type:
    setattr(
        cls,
        '_{}__log'.format(cls.__name__),
        logging.getLogger(cls.__module__ + '.' + cls.__name__)
    )
    return cls


# érzékelők, mérőberendezések csatlakoztatásának ellenőrzéséhez
def sensor_pluggedin(ifdata: tuple) -> bool:
    print(ifdata[0], ifdata[1], ' -> ', end=' ')
    if ifdata[0] == 'USB':
        lsusb_out = Popen(f'lsusb | grep \'{ifdata[1]}\'', shell=True, stdin=None, stdout=PIPE, close_fds=True) \
            .stdout.read().strip().decode('utf-8').split(' ')
        if len(lsusb_out) > 3:
            bus, dev = lsusb_out[1], lsusb_out[3][:-1]
            print(f'/dev/bus/usb/{bus}/{dev}')
            return True
    elif ifdata[0] == 'I2C':
        hexid = ifdata[1][2:]
        rowid = hexid[0] + '0'
        i2c_out = Popen(f'i2cdetect -y 1 | grep \'{rowid}\'', shell=True, stdin=None, stdout=PIPE, close_fds=True) \
            .stdout.read().strip().decode('utf-8')
        if hexid in i2c_out:
            print(i2c_out)
            return True
    elif ifdata[0] == 'RPICAM':
        cam_out = Popen('vcgencmd get_camera', shell=True, stdin=None, stdout=PIPE, close_fds=True) \
            .stdout.read().strip().decode('utf-8')
        if 'detected=1' in cam_out:
            print('OK')
            return True
    print('-?-')
    return False
