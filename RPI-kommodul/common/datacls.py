#!/usr/bin/python3
# -*- coding: utf-8 -*-

from datetime import datetime, timezone
import json
import os
import math


###############################
#   TA120 json adat kezelés   #
###############################
class TA120adat:

    # konstansok adatkiolvasáshoz (CESVA TA120 protocols pdf-ből)
    ZAJSZINT = '-N'
    TULLOVES = '-O'
    ALULLOVES = '-U'
    IDOSOR = '-S'

    def __init__(self, jsondata):
        self._data = jsondata

    def sorozatszam(self):
        return self._data['sensors'][0]['sensor'].split('-')[1][1:]

    def _getData(self, adattipus, adatnev):
        sn = self.sorozatszam()
        for item in self._data['sensors']:
            if item['sensor'] == 'TA120-T' + sn + adattipus:
                return item['observations'][0][adatnev]
        return False

    def LAeq(self):
        return self._getData(self.ZAJSZINT, 'value')

    def idobelyeg(self):
        return datetime.strptime(
            self._getData(self.ZAJSZINT, 'timestamp'),
            '%d/%m/%YT%H:%M:%S%Z'
        )

    def unixtime(self):
        return self.idobelyeg().timestamp()     # ez valójában UTC

    def percbelyeg(self):
        return self.idobelyeg().strftime('%Y-%m-%d__%H:%M')

    def idosor(self):
        return self._getData(self.IDOSOR, 'value')

    def percadatsor(self):
        # percet külön is beletesszük, hogy az adattovábbítási határidők könnyen követhetők legyenek
        return {
            'sorozatszam': self.sorozatszam(),
            'percbelyeg': self.percbelyeg(),
            'perc': int(self.percbelyeg().split(':')[1]),
            'LAeq': self.LAeq(),
            'idosor': self.idosor(),
            'unixtime': self.unixtime()
        }


######################################
#   METEOROLÓGIA json adat kezelés   #
######################################
doAVG = True
getLastData = False


class METadat:

    task_for_metitem = {
        'time': getLastData, 'model': getLastData, 'id': getLastData, 'battery_ok': getLastData,
        'rain_mm': doAVG, 'temperature_C': doAVG, 'humidity': doAVG, 'light_lux': doAVG,
        'wind_dir_deg': doAVG, 'wind_avg_m_s': doAVG, 'wind_max_m_s': doAVG,
    }
    cmdutil = 'rtl_433'
    usbID = '0bda:2838'     # Realtek Semiconductor Corp. RTL2838 DVB-T

    def __init__(self, station_freq, station_model, station_id):
        self._avg_data = dict()
        self._idosor = dict()
        self._rtl_freq = station_freq
        self._model = station_model
        self._id = station_id

    def alapallapot(self):
        self._avg_data = dict()
        self._idosor = dict()

    def feldolgoz(self, data):
        self._idosor[data['time']] = dict(filter(lambda elem: elem[0] != 'time', data.items()))
        for item in self.task_for_metitem.keys():
            if len(self._avg_data) < len(self.task_for_metitem) or self.task_for_metitem[item] == getLastData:
                if data.get(item, None) is not None:
                    if isinstance(data[item], (int, float)):
                        self._avg_data[item] = float(data[item])
                    else:
                        self._avg_data[item] = data[item]  # ha pl. a windDir = None, mert nincs szél
            elif self.task_for_metitem[item] == doAVG:
                if data.get(item, None) is not None:
                    if isinstance(data[item], (int, float)):
                        if isinstance(self._avg_data[item], float):
                            self._avg_data[item] = (self._avg_data[item] + float(data[item])) / 2
                        else:
                            pass  # megtartjuk inkább a korábbi adatot (hátha szám)

    def cmd(self):
        # beállítások
        #   -f frekvencia
        #   -F json    (formátumú kimenet)
        #   -C si      (mértékegységrendszer)
        #   -E quit    (adatolvasás után kilépés)
        #   -T timeout (s)
        return f'rtl_433 -f {self._rtl_freq}M -F json -C si -E quit -T 60'

    def model(self):
        return self._model

    def id(self):
        return self._id

    def sorozatszam(self):
        return self._model + '-#' + str(self._id)

    def idobelyeg(self):
        return datetime.\
            strptime(self._avg_data['time'] + ' CEST', '%Y-%m-%d %H:%M:%S %Z').\
            astimezone(timezone.utc)

    def perc(self):
        return int(self._avg_data['time'].split(':')[1])

    def unixtime(self):
        return self.idobelyeg().timestamp()

    def percbelyeg(self):
        return self.idobelyeg().strftime('%Y-%m-%d__%H:%M')

    def idosor(self):
        return self._idosor

    def harmatpont(self):
        # harmatpont számítás hőmérséklet T [Cfok] és páratartalom Rh [%] ismeretéből:
        #   Tp = b * f(T, Rh) / (a - f(T, Rh))
        #   f(T, Rh) = a * T / (b + T) + ln(Rh / 100)
        #   a = 17,27   b = 237,7
        a = 17.27
        b = 237.7
        T = self._avg_data['temperature_C']
        Rh = self._avg_data['humidity']

        def f():
            return a * T / (b + T) + math.log(Rh / 100)
        pass

        return b * f() / (a - f())

    def felhoalap(self):
        # felhőalap számítás hőmérséklet T [Cfok] és harmatpont [Cfok] ismeretéből:
        #   z = (T - Tp) * 120
        return round(
            (self._avg_data['temperature_C'] - self.harmatpont()) * 120,
            0
        )

    def besugarzas(self):
        # fényerősség átszámítás:
        #   W/m2 = lux / 126,7
        return round(self._avg_data['light_lux'] / 126.7, 1)

    def eso(self):
        # valamiért van egy 999-es alapérték, ami a 0-nak felel meg
        return self._avg_data['rain_mm'] - 999

    @staticmethod
    def legnyomas():
        # a légnyomásmerő jelét az RTL modul nem simeri, ezért a dobozból vesszük
        from . import sensors
        with sensors.THPMETER() as thp:
            _, _, legnyom = thp.getData()
            return legnyom

    def percadatsor(self):
        # percet külön is beletesszük, hogy az adattovábbítási határidők könnyen követhetők legyenek
        return {
            'sorozatszam': self.sorozatszam(),
            'percbelyeg': self.percbelyeg(),
            'perc': int(self.percbelyeg().split(':')[1]),
            'homerseklet': self._avg_data['temperature_C'],
            'harmatpont': self.harmatpont(),
            'relpara': self._avg_data['humidity'],
            'legnyomas': self.legnyomas(),
            'szelirany': self._avg_data['wind_dir_deg'],
            'szelseb': self._avg_data['wind_avg_m_s'],
            'szellok_seb': self._avg_data['wind_max_m_s'],
            'szellok_irany': self._avg_data['wind_dir_deg'],
            'eso': self.eso(),
            'besugarzas': self.besugarzas(),
            'felhoalap': self.felhoalap(),
            'idosor': self.idosor(),
            'unixtime': self.unixtime(),
            'elem_allapot': self._avg_data['battery_ok'],
        }

    def szeladatok(self):
        # levegős mérésekhez
        return {
            'szelirany': self._avg_data['wind_dir_deg'],
            'szelseb': self._avg_data['wind_avg_m_s'],
        }

    def usbdongle_reset(self) -> bool:
        with os.popen(f'usbreset {self.usbID}', 'r') as proc:
            output = proc.read().strip()
        if 'Resetting RTL2838UHIDIR ... ok' in output:
            return True
        return False


#############################################
#   Hangfelvétel rögzítés opciók kezelése   #
#############################################
class REC:

    def __init__(self):
        self._cmd = 'arecord'   # a parancssori alkalmazás, amivel rögzítünk

    @staticmethod
    def device() -> str:
        return '-D plughw:1,0'

    @staticmethod
    def format() -> str:
        return '--format=S16_LE -V mono'

    @staticmethod
    def duration(sec: int) -> str:
        return f'--duration={sec}'

    @staticmethod
    def rate(hz: int = 8000) -> str:
        return f'--rate={hz}'

    def cmd(self, perchossz: int, fajlnev: str):
        dur_sec = (perchossz * 60) - 3      # kicsit rövidebb, hogy biztos ne lógjanak egybe a felvételek
        return ' '.join([
            self._cmd, self.device(), self.format(), self.duration(dur_sec), self.rate(), fajlnev
        ])


# egyszerű teszt
if __name__ == "__main__":

    try:
        with open('proba/sentilo-minta.json', 'r') as sminta:
            td = TA120adat(json.loads(sminta.read()))
        print(td.sorozatszam(), td.percbelyeg(), td.unixtime(), td.LAeq())
        print(td.idosor())
    except IOError:
        print('sentilo-minta.json fájl nem elérhető a teszteléshez')

    md = METadat(868.2, 'Fineoffset-WH24', 201)
    md.feldolgoz(
        json.loads(
            '{"time" : "2021-07-08 16:07:26", "model" : "Fineoffset-WH24", "id" : 201, "battery_ok" : 1, \
            "temperature_C" : 38.700, "humidity" : 26, "wind_dir_deg" : 6, "wind_avg_m_s" : 1.820, \
            "wind_max_m_s" : 2.240, "rain_mm" : 332.100, "uv" : 1892, "uvi" : 4, \
            "light_lux" : 66495.000, "mic" : "CRC"}'
        )
    )
    print(md.sorozatszam(), md.percbelyeg(), md.unixtime())
    print(md.percadatsor())
    print('RESET: ', md.usbdongle_reset())

    print(REC().cmd(5, 'probafajl'))
