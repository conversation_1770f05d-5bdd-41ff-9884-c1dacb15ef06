#!/bin/bash

# Restart network interfaces
# if wwan0 connection is down
# --------------------------

PING="/bin/ping -q -c1 -W 10 -I wwan0 8.8.8.8"

# shellcheck disable=SC2069
if ! ${PING} 2>&1 > /dev/null
then
  /usr/bin/logger "GSM (wwan0) kapcsolat hiányzik! Újrainicializálás..."
  # ez már magában foglal egy reset-et is
  /sbin/ifdown --force wwan0
  /usr/bin/logger "wwan0 interface kilőve"
  /bin/sleep 3

  CHECKDHC=$(/usr/bin/pgrep dhclient)
  if [ -z "$CHECKDHC" ]
  then
    /usr/bin/logger "dhclient rendben leállt OK"
  else
    /usr/bin/logger "dhclient be van ragadva -> kill neki"
    /usr/bin/killall dhclient
  fi

  /usr/bin/logger "simcom kernel modul reset"
  /sbin/rmmod simcom_wwan
  /bin/sleep 5
  /sbin/modprobe simcom_wwan
  /bin/sleep 3
  /sbin/ifconfig wwan0 up

  /usr/bin/logger "wwan0 interface újra"
  /sbin/ifup wwan0
  /usr/bin/logger "ifup exit code: $?"
  /bin/sleep 5

  CHECK="/sbin/ifconfig wwan0"
  if ! ${CHECK}
  then
    /usr/bin/logger "GSM (wwan0) még mindig nem jó -> AT CRESET és majd következőre új próba"
    /usr/bin/wvdial wwan-reset
  else
    /usr/bin/logger "GSM (wwan0) kapcsolat OK!"
  fi
else
  /usr/bin/logger "GSM kapcsolat rendben -> megy a ping"
  /usr/bin/wget -q -O - https://zajmonitor.imagmernok.hu/api/v1/ping > /home/<USER>/rendszer/lastping
fi
