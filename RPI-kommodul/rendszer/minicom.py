#!/usr/bin/python3

import RPi.GPIO as GPIO
import serial
import time

ser = serial.Serial("/dev/ttyUSB2",115200)
ser.flushInput()

power_key = 6
command_input = ''
rec_buff = ''

def power_on(power_key):
	print('SIM7600X is starting:')
	GPIO.setmode(GPIO.BCM)
	GPIO.setwarnings(False)
	GPIO.setup(power_key,GPIO.OUT)
	time.sleep(0.1)
	GPIO.output(power_key,GPIO.HIGH)
	time.sleep(2)
	GPIO.output(power_key,GPIO.LOW)
	time.sleep(5)
	ser.flushInput()
	print('SIM7600X is ready')

def power_down(power_key):
	print('SIM7600X is loging off:')
	GPIO.output(power_key,GPIO.HIGH)
	time.sleep(3)
	GPIO.output(power_key,GPIO.LOW)
	time.sleep(5)
	print('Good bye')


#power_on(power_key)
print('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: quit')

while True:
    command_input = input('>> ')
    if command_input.strip() == 'quit':
        break
    ser.write((command_input+'\r\n').encode())
    time.sleep(0.1)
    if ser.inWaiting():
        time.sleep(0.01)
        rec_buff = ser.read(ser.inWaiting())
    if rec_buff != '':
        print(rec_buff.decode())
        rec_buff = ''

ser.close()
#power_down(power_key)

