name: Build Analyze Check
on: [push, pull_request]
jobs:
  build_check_job:
    runs-on: ubuntu-latest
    name: Build with CMake
    steps:
    - name: Checkout
      uses: actions/checkout@v2
    - name: Run CMake+Ninja
      uses: lukka/run-cmake@v3
      with:
        cmakeListsOrSettingsJson: CMakeListsTxtAdvanced
        buildDirectory: '${{runner.workspace}}/b/ninja'
        cmakeAppendedArgs: '-GNinja -DENABLE_RTLSDR=OFF -DENABLE_SOAPYSDR=OFF'
    - name: Run CMake+UnixMakefiles
      uses: lukka/run-cmake@v3
      with:
        cmakeListsOrSettingsJson: CMakeListsTxtAdvanced
        buildDirectory: '${{runner.workspace}}/b/unixmakefiles'
        cmakeAppendedArgs: '-G "Unix Makefiles" -DENABLE_RTLSDR=OFF -DENABLE_SOAPYSDR=OFF'
  style_check_job:
    runs-on: ubuntu-latest
    name: Check code style
    steps:
    - uses: actions/checkout@v2
    - name: Style Check
      uses: ./.github/actions/style-check
  maintainer_update_check_job:
    runs-on: ubuntu-latest
    name: Needs maintainer_update
    steps:
    - uses: actions/checkout@v2
    - name: Working directory clean excluding untracked files
      run: |
        ./maintainer_update.py
        [ -z "$(git status --untracked-files=no --porcelain)" ]
  analyzer_check_job:
    # https://github.com/actions/virtual-environments
    # - Ubuntu 20.04 	ubuntu-20.04 	ubuntu-20.04
    # - Ubuntu 18.04 	ubuntu-latest or ubuntu-18.04
    # https://apt.llvm.org/
    # - Bionic (18.04)
    # - Focal (20.04)
    runs-on: ubuntu-18.04
    name: Analyze with Clang
    steps:
    - uses: actions/checkout@v2
    - name: Install Clang
      run: |
        wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key 2>/dev/null | sudo apt-key add -
        sudo add-apt-repository 'deb http://apt.llvm.org/bionic/ llvm-toolchain-bionic-11 main' -y
        sudo apt-get update -q
        sudo apt-get install -y clang-11 lld-11 libc++-11-dev libc++abi-11-dev clang-tools-11
    - name: Clang Analyzer
      # excludes include/mongoose.h src/mongoose.c include/jsmn.h src/jsmn.c
      # exit code 1 if there is output
      run: |
        clang -Iinclude --analyze -Xanalyzer -analyzer-output=text -Xanalyzer -analyzer-disable-checker=deadcode.DeadStores include/[a-ikln-z]*.h src/[a-ikln-z]*.c src/devices/*.c 2>&1 | tee analyzer.out
        [ ! -s analyzer.out ]
