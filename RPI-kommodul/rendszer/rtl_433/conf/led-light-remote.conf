# Generic Remote Controller for LED Strip Lights
# Remote decoder from issue #1112 by chaos511

# first 16 bit seem to be a fixed remote id
# last 8 bit encode the button press:
# (the keypad has up to 7 rows of 3 columns,
# usually some keys are missing)
#  1  2  3
#  4  5  6
#  7  8  9
# 10 11 12
# 13 14 15
# 16 17 18
# 19 20 21

decoder {
    name=LED-Light-Remote,
    modulation=OOK_PWM,
    short=264,
    long=1060,
    reset=2000,
    gap=0,
    tolerance=316,
    bits=25,
    invert,
    get=@0:{16}:id,
    get=@16:{8}:button
}
