# EC3k Energy Count Control
#
# "Voltcraft Energy Count 3000" (868.3 MHz) sensor sold by Conrad
# aka “Velleman NETBSEM4”
# aka “La Crosse Technology Remote Cost Control Monitor – RS3620”.
# aka "ELV Cost Control"
#
# Stub driver
# FSK PCM NRZ 50 us bit width, up to 12 zeros seen, package should be around 578 bits.
#
# Copyright (C) 2015 Tommy Vestermark

decoder {
    name=Energy-Count-3000,
    modulation=FSK_PCM,
    short=50,
    long=50,
    reset=800,
    bits>=550,
    bits<=590
}
