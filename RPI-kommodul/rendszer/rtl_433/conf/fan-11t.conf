
#
# FAN-11T Remote Control of Harbor Breeze Fan
#
# KUJCE9103 FAN-11T FAN-53T 2AAZPFAN-53T
#
# written: <PERSON>
# 
# https://www.amazon.com/s?k=Fan-11T
# 
# Based on the Holtek the HT12E/HT12F chipsets
# 
# HT12E : https://www.holtek.com/documents/10179/116711/2_12ev120.pdf
# HT12F : https://www.holtek.com/documents/10179/116711/2_12dv120.pdf
# 
# FCC ID: L3HFAN11T
#
# https://fccid.io/L3HFAN11T
# 
# 0.36ms Short
# 0.71ms Long
# 
# 12bits of info ( 13 bits transmitted )
# 
# receiver requires three matching transmissions optionally followed by one packet with all shorts
# 
# The Fan-11T uses a 4 bits dip-switch as an identifier
# 
# packets can be described as
# 
#    <short> <long> + 4 bit ID + <short> + 6 bit command
# 
# The follow table uses '1001' as the ID code:
# if short is 0 and Long is 1
# 
#    Hi      0 1 1 0 0 1 0 1 0 0 0 0 0   =   0110010100000 = 3232 = 0xca0
#    Med     0 1 1 0 0 1 0 0 1 0 0 0 0   =   0110010010000 = 3216 = 0xc90
#    Low     0 1 1 0 0 1 0 0 0 1 0 0 0   =   0110010001000 = 3208 = 0xc88
#    Off     0 1 1 0 0 1 0 0 0 0 0 1 0   =   0110010000010 = 3202 = 0xc82
#    Lit     0 1 1 0 0 1 0 0 0 0 0 0 1   =   0110010000001 = 3201 = 0xc81
#    End?    0 1 1 0 0 1 0 0 0 0 0 0 0   =   0110010000000 = 3200 = 0xC80
#
#
#
# 0110010000010

# rtl_433 -R 0 -f 302450000 -c fan-11t.conf

# The FCC Documentation list the frequency at 303.9MMz but the devices tests out to between 303.4 and 303.5 (depending on battery)


# To reduce false positives, uncomment the match
# field and set the value to your remote's dip switch pattern

# eg: if your Dip-switch setting is up-down-up=up ( '1011' )
# set the match setting in the decoder definition to 0x6C
# for example:
#     match={7}0x66C


#`    Dip Switch     match preamble
#	0000		0x40
#	0001		0x44
#	0010		0x48
#	0011		0x4C
#	0100		0x50
#	0101		0x54
#	0110		0x58
#	0111		0x5C
#	1000		0x60
#	1001		0x64
#	1010		0x68
#	1011		0x6C
#	1100		0x70
#	1101		0x74
#	1110		0x78
#	1111		0x7C



frequency 302.400M

decoder {
    name        = Fan-11t,
    modulation  = OOK_PWM,
    short       = 360,
    long        = 710,
    gap         = 0,
    reset       = 5000,
    tolerance   = 50,
    unique,
    invert,
    bits        = 13,
    get         = id:@2:{4},
    get         = button:@7:{6}:[32:fan_Hi 16:fan_Med 8:fan_Low 2:fan_Off 1:light 0: 4: ],
}

