# config for PIR-EF4SBT00003.
# <PERSON> - 25/04/2019
#
# The sensor is known with the FCC-ID: EF4SBT00003
#
# Some references:
# * https://fcc.report/FCC-ID/EF4SBT00003
# * http://certid.org/fccid/EF4SBT00003
# * https://apps.fcc.gov/eas/GetEas731Report.do?applicationId=XkBhwjLuvh2pXcri3MP%2FWA%3D%3D&fcc_id=EF4SBT00003
#
#
# The PIR-EF4 was produced by Nortek (nortekcontrol.com)
# It was released in 1996 and works with 9 Volts battery.
# The sensor broadcast its ID when it is triggered.
# The ID is defined on 2 bytes.
# There is no CRC or other data transmitted.
# The modulation is OOK with PPM at the frequency of 315MHz. :
# Guessing modulation: Pulse Position Modulation with fixed pulse width
# Attempting demodulation... short_width: 848, long_width: 2132, reset_limit: 8488, sync_width: 0
# Use a flex decoder with -X 'n=name,m=OOK_PPM,s=848,l=2132,g=2136,r=8488'
#

# PIR-EF4 configuration:
decoder n="PIR-EF4 sensor",m=OOK_PPM,s=848,l=2116,r=8488,rows=1,bits=16,get=@0:{16}:id
