# Friedland EVO door bell
#
# This decodes the transmissions from the Friedland EVO wireless door bell buttons. The
# Friedland doorbell system allows multiple buttons and multiple bells and will make a
# different sound depending on which door button was pressed. This has only been tested with
# 2 different buttons.
#
# In order to eliminate other transmissions the match function has been used with part of
# the data that was the same between the two tested variants. This may not hold true for
# different devices so be prepared to remove/adjust as needed.
#
# The frequency is around 433.8Mhz
#
# Note: The buttons send out the same data 12 times so you may need to write a filter
# to pipe the output through to remove them
#

decoder {
    name        = FriedlandEvo,
    modulation  = OOK_PCM,
    short       = 750,
    long        = 750,
    reset       = 9000,
    preamble    = AA,
    match       = 0x410408210400,
    unique
}
