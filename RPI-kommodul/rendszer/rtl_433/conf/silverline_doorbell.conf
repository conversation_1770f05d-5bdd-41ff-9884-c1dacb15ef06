# Silverline doorbell

# A typical x1527 OTP encoder device
# sends 12 tristate "bits" encoded as 2 bits each and a sync pulse

decoder {
    name        = Silverline-Doorbell,
    modulation  = OOK_PWM,
    short       = 120,
    long        = 404,
    gap         = 468,
    reset       = 4472,
    bits        = 25,
    get         = channel:@1:555,
    get         = sound:@17:15,
}

# The flex spec supports selecting random bits with a getter mask. E.g.
# in this x1527 tristate example every odd bit is `1` and you want to get
# 6 bits "channel" from the first 12 bits (discarding every odd bit) use:
#     get=channel:@1:555
# Effectively this is 11 bits (starting with 1 bit offset to discard the
# first bit) because the mask is always aligned on the first set bit
# (i.e. 555, aaa, 000555, ... are identical).
#
# Imagine the 25 bits of the transmission as:
#     1?1?1?1?1?1?1?1?1?1?1?1?1
# The mask `555` is:
#     0000 0101 0101 0101
# aligned to the first set bit
#     10101010101
# offset 1 bit (`@1`) it selects like this:
#     1?1?1?1?1?1?1?1?1?1?1?1?1
#      10101010101
#     ->
#      ? ? ? ? ? ?
# resulting in that 6 bit value `??????`.
#
# The same way 3 bits "sound" is `get=sound:@17:15` (where `15` here is
# hex for the 5-bit pattern `10101` -- or use `a8` if you want to left align
# the pattern in the byte there).
