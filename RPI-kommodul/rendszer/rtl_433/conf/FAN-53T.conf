# Decoder for FAN-53T ceiling fan/light remote control
# Very similar to FAN-11T.
#
# https://fccid.io/2AAZPFAN-53T/User-Manual/User-manual-2228959
#
# Code Format: 01 <Dipswitch Code> 0 <6-bit button map>  (all bits inverted)
# Button Map Bits: [fan1, fan2, fan3, (some remotes send this when the button is released), fan0, light]
# 
# Dip switches are used as a unique ID to talk to a specific unit
#
# Some remotes have buttons labeled 0,1,2,3
# Others are labeled Off, Hi, Med, Low
#
# Fan 0 = "Off"
# Fan 1 = "High"
# Fan 2 = "Medium"
# Fan 3 = "Low"
# 
# Dipswitch: 0000
# Fan 1 - 01 0000 0 100000
# Fan 2 - 01 0000 0 010000
# Fan 3 - 01 0000 0 001000
# Fan 0 - 01 0000 0 000010
# Light - 01 0000 0 000001
# 
# Dipswitch: 0001
# Fan 1 - 01 0001 0 100000
# Fan 2 - 01 0001 0 010000
# Fan 3 - 01 0001 0 001000
# Fan 0 - 01 0001 0 000010
# Light - 01 0001 0 000001
# 
# Dipswitch: 1000
# Fan 1 - 01 1000 0 100000
# Fan 2 - 01 1000 0 010000
# Fan 3 - 01 1000 0 001000
# Fan 0 - 01 1000 0 000010
# Light - 01 1000 0 000001
 

frequency 303.900M

decoder {
    name        = FAN-53T,
    modulation  = OOK_PWM,
    short       = 360,
    long        = 700,
    gap         = 0,
    reset       = 2000,
    invert,
    bits        = 13,
    get         = id:@2:{4},
    get         = button:@7:{6}:[32:fan_Hi 16:fan_Med 8:fan_Low 4:button_Released 2:fan_Off 1:light 0:  ],
}