# Decoder for the Mighty Mule FM231 Driveway alarm from GTO Inc
# FCC Test report, including RF waveforms is here:
#   https://fccid.io/I6HGTOFM231/Test-Report/Test-Report-1214140.pdf

# Use the Accurite and similar convention for reporting battery.
# The name is 'battery_ok' with values 1 (ok) and 0. (which are
# numerically reversed from what the FM231 reports)

# The DIP switches for setting a unique device ID are labeled 1-4
# from left to right, but appear in the # data stream in reverse
# order. 

decoder {
    name=MightyMule-FM231,
    modulation=OOK_PWM,
    short=650,
    long=1200,
    sync=3800,
    reset=1100,
    tolerance=200,
    rows=1,
    bits=9,
    get=@4:{1}:battery_ok:[0:1 1:0],
    get=@5:{4}:id,
    unique
}
