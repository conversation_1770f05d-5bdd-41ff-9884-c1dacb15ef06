# Equation/Siemens ADLM FPRF on 433.863MHz
# 3 zones heater programer
#
# A 50ms wakeup pulse followed by a 5ms gap,
# then a start pulse 5ms gap + 3ms pulse followed by 41 data pulses.
# This is repeated 3 times with the next wakeup directly following
# the preceding stop pulses.
#
# Bit width is 2000 us with
# Short pulse: ___- 1500us gap +  500 us pulse
# Long pulse:  _---  500us gap + 1500 us pulse
#
# This is a electric heater programmer sold in France by <PERSON> on the brand Equation.
# It is manufactured by Siemens, and also has the mark RDE100.1 FPRF on the PCB

decoder {
    name=ADLM FPRF,
    modulation=OOK_PWM,
    short=500,
    long=1500,
    reset=7000,
    gap=2000,
    bits>=40,
    get=@8:{16}:id,
    get=@28:{4}:zone,
    get=@32:{4}:mode:[9:ECO 10:COMFORT 8:OFF],
    unique
}
