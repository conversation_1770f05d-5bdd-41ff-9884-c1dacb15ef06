# Tesla charge port opener
#
#  * When the button on the charge handle is pressed the signal is repeated at 0.15s intervals for 10 times.
#  * There are no unique codes or transmissions variants. The code is the same for all Tesla charge port
#    handles and there is a single button.
#  * The transmitter hardware is reported to be using a Si4010.
#  * tolerance=20 worked in the initial test, larger value in the configuration for additional tolerance

decoder {
    name       = Tesla charge port opener,
    modulation = OOK_MC_ZEROBIT,
    short      = 400,
    reset      = 1200,
    tolerance  = 50,
    match      = 094aa9b38da19,
    rows       = 5
    repeats    = 2,
    countonly,
}
