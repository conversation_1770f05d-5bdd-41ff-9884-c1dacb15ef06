# Sonoff RM433 remote controller
# https://www.itead.cc/sonoff-rm433-remote-controller-base.html

# map the 8 buttons to A->H
# Each remote controller has its own ID (2 first bytes)

decoder {
    name=Sonoff-RM433,
    modulation=OOK_PWM,
    short=260,
    long=744,
    reset=8000,
    gap=800,
    tolerance=50,
    bits>=24,
    invert,
    get=@0:{20}:id,
    get=@20:{4}:button:[8:A 12:B 4:C 9:D 2:E 5:F 1:G 3:H],
    unique
}
