# Decoder for Salus RT300RF thermostat
# listen on 868.286Mhz
# rtl_433 -Y classic -R 0 -X "name=SalusRT300RF, m=FSK_PCM, s=833, l=833, r=16000, preamble={24}0xaaaaaa,get=@0:{16}:Thermostat ID, get=@28:{4}:heat:[1:ON 2:OFF]" -f 868.286Mhz -F "mqtt://*************,retain=1,devices=sensors/rtl_433/P[protocol]/C[channel]"
# Run with:
#rtl_433 -Y classic -R 0 -c /home/<USER>/.config/rtl_433/SalusRT300RF.conf -f 868.286Mhz -M newmodel

# Report iso time:
report_meta time:iso

# Including the 'report_meta level' switch (commented out immediately below) means information on the signal quality and strength are added to the output. 
#report_meta level

# specify MQTT output and formatting - replace the IP address (*************) with your mosquitto broker's IP address and port number (I didn't need the port number)
output mqtt://*************,retain=1,devices=rtl_433/Salus[/id]


decoder {
  name        =  SalusRT300RF, 
  m           =  FSK_PCM, 
  s           =  833, 
  l           =  833, 
  r           =  16000, 
  unique,
  preamble    =  {24}0xaaaaaa,
  get         =  @0:{8}:id, 
  get         =  @28:{4}:Heat:[1:ON 2:OFF]
}
