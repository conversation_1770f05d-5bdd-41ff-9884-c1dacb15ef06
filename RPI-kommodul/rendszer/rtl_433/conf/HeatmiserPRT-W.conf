# Decoder for Heatmiser PRT-W thermostat with mqtt output

# These are thermostats principally designed to control underfloor heating systems although they also can be used to control radiators.

# The thermostat transmits to a receiver which controls physical valves to provide heat to the zone where the thermostat is. I think the receiver also has a hard wire link to the boiler to tell it to wake up and get heating when this happens, as I've detected no signal to suggest it's wireless.

# The thermostats all transmit on or around 869.01Mhz

# I run the conf file from the command line with:
#rtl_433 -Y classic -R 0 -c /home/<USER>/.config/rtl_433/HeatmiserPRT-W.conf -f 869.01Mhz -M newmodel

# You will need to change the prettified channel codes to match your thermostat's output.

# The heatmiser's bit stream at bit no. 8 gives the receiver address for 8 bits, the next 4 bits is the on/off command and the next 4 is the thermostat ID number. The consequence of all this is that to get a meaningful unique ID we need both the receiver and the stat ID. I don't know how to concatenate the first 8bits, skip 4 and then add a further 4. To keep things simple, I just took the entire 16 bits, but this means we end up with two values for each thermostat - one for the on state and one for the off state. Thus on my system both 0x8821 and 0x8831 relate to the Living room thermostat. I then just aliased both bitwise outputs to the same text string: Living_Room. This seems to work ok, but feels like a bit of hack.
#
# You will also need to change the receiver location to be meaningful for your system. Mine was configured with two receivers, one on ground floor (L00) and another on the second floor (L02)
#
#get=@8:{16}:Stat Name:[0x8821: Living_Room 0x8831:Living_Room 0x8921:Family_Room 0x8931:Family_Room 0x8922:Kitchen 0x8932:Kitchen 0x8923:Hallway 0x8933:Hallway],


# Report iso time:
report_meta time:iso

# Including the 'report_meta level' switch (commented out below) means information on the signal quality and strength are added to the output. This was useful to me to determine which thermostat was which without running down 2 flights or stairs everytime I wanted to make a change. Basically the higher the rssi number, generally, the closer the stat was physically to my SDR aerial.

#report_meta level

# specify MQTT output and formatting - replace the IP address (*************) with your mosquitto broker's IP address and port number (I didn't need the port number)
output mqtt://*************,retain=1,devices=rtl_433[/channel]

decoder {
  name        =   Heatmiser_PRT-W_Thermostat, 
  modulation  =   FSK_PCM, 
  s           =   416, 
  l           =   416, 
  r           =   20000, 
  unique,
  bits        >=  120, 
  preamble    =   aaaa2dd4,
  get         =   channel:@8:{16}:[0x8821:10 0x8831:10 0x8921:20 0x8931:20 0x8922:30 0x8932:30 0x8923:40 0x8933:40],
  get         =   StatName:@8:{16}:[0x8821: Living_Room 0x8831:Living_Room 0x8921:Family_Room 0x8931:Family_Room 0x8922:Kitchen 0x8932:Kitchen 0x8923:Hallway 0x8933:Hallway],
  get         =   Receiver:@8:{8}:[0x88:L02 0x89:L00], 
  get         =   StatID:@20:{4},    
  get         =   event:@16:{4}:[0x3:ON 0x2:OFF],
}



