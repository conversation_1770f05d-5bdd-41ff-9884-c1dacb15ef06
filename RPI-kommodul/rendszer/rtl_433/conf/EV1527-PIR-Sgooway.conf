# EV1527 based passive infrared sensor
#
# This decoder reads detections of the Sgooway EV1527 based PIR
# 
# The shown example can distinguish between multiple of those PIR's (two in this case)
# and present the different PIR's as "channel : x" in the output. Adopt this line
# to your EV1527 unique codes. Put the decimal representation of the in here.
#
# If this is not desired, a simple "match=abcde" can limit the detection of
# one unique EV1527 code.

decoder {
        n=EV1527-PIR,
        m=OOK_PWM,
        s=400,
        l=1200,
        g=1500,
        r=12000,
        repeats>=4,
        bits=25,
        get=@0:{25}:channel:[12345678:1 98765432:2],
        unique
}
