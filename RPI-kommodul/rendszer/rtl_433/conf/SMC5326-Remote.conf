# SMC5326 remote control

# This decoder reads two or four button pressed on the remote control. The 
# rubber buttons are directly connected to the SMC5326 data pins. Therefore, 
# pressing more than one button at the same time is possible. However this 
# flex spec only match a single button press
#
# SMC5326 are usually configured using 8-dip switch. All possible combinations 
# of keys are matched. You can simply press any button to 
# determine the remote unique keys ;-)
#
# see rtl_433_tests/tests/smc5326 for more information

decoder {
        n=SMC5326-Remote,
        m=OOK_PWM,
        s=328,
        l=948,
        g=1400,
        r=2000,
        bits=26,
        invert,
        get=@0:{4}:key01:[15:++ 14:+f 12:+- 11:f+ 10:ff 8:f- 3:-+ 2:-f 0:--],
        get=@4:{4}:key23:[15:++ 14:+f 12:+- 11:f+ 10:ff 8:f- 3:-+ 2:-f 0:--],
        get=@8:{4}:key45:[15:++ 14:+f 12:+- 11:f+ 10:ff 8:f- 3:-+ 2:-f 0:--],
        get=@12:{4}:key67:[15:++ 14:+f 12:+- 11:f+ 10:ff 8:f- 3:-+ 2:-f 0:--],
        get=@16:{8}:button:[234:A 186:B 174:C 171:D]
}
