# CAME-TOP432.conf
# Copyright (C) 2020 JFORESTIER
# Decode CAME remote control TOP-432EV, TOP-432NA, TOP-432EE.
# This remote control is used for garage door and sliding gate. 
# It transmits on 433.92 MHz (as it is written on the case), built since 2006 
# (as said on the FCC site https://www.fcc.gov/oet/ea/fccid with reference M48 TOP-NA)
# 
# It works with CAME radio receiver cards "AF43S", capable of handling 4096 codes. 
# CAME is an italian company. Theses remote controls are mainly sold in europe (France, Italy, Belgium). 
# https://www.came.com and https://www.came-europe.com .

# Device information and test files:
# https://github.com/psa-jforestier/rtl_433_tests/tree/master/tests/Came/TOP432
# The device uses PWM encoding,
# - 0 is encoded as 320 us gap and 640 us pulse,
# - 1 is encoded as 640 us gap and 320 us pulse.
# The device sends a 4 times the packet when a button on the remote control is pressed.
# A transmission starts with a 320 us pulse. At the end of the packet, there is a minimum of 36 periods of 320us between messages (11520us)

decoder {
  name=CAME-TOP432,
  modulation=OOK_PWM,
  short=320,
  long=640,
  reset=10000,
  bits=13,
  gap=830,
  preamble={1}8,
  get=@1:{12}:button_code
}
