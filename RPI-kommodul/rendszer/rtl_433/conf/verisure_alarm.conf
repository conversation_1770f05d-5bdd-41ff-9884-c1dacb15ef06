# Verisure Alarm, contact switch made by Honeywell
# Copyright (C) 2020 <PERSON>
#
# The payload is most likely encrypted, but you can still use the id
# and state to figure out if the contact switch is opened or closed.
#
# This decoder needs the classic FSK demodulator
#
#55fff d87e08023c005e1b3e2ed 54000 387e17fdc3ffbe04c1d13
#55fff d87e08023c005e1b3e2ed 54000 387e17fdc3ffbe04c1d13
#55fff d87e08023c005e1b3e2ed 54000 387e17fdc3ffbe04c1d13
#55fff d87e08023c005e1b3e2ed 54000 387e17fdc3ffbe04c1d13
#55fff d87e08023c005e1b3e2ed 54000 387e17fdc3ffbe04c1d13
#
# The bit stream might be 2 different packets of ca 10 bytes that
# are repeated several times.

decoder {
    name=Verisure Alarm,
    modulation=FSK_PCM,
    short=208,
    long=208,
    reset=4025,
    gap=2500,
    tolerance=10,
    match={20}0xafffe,
    get=@24:{24}:id,
    get=@52:{12}:state,
}


