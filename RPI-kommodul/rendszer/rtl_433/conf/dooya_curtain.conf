# Dooya Curtain Remote (DC1602)
# e.g. https://www.aliexpress.com/i/32954197821.html
# see https://github.com/merbanan/rtl_433/issues/1545
#
# A 15 channel remote, with 3 functions/commands: Open/Close/Stop

decoder {
    name=<PERSON><PERSON>-Curtain,
    modulation=OOK_PWM,
    short=350,
    long=750,
    sync=4900,
    gap=990,
    reset=9900,
    bits>=40,
    invert,
    get=@0:{24}:id,
    get=@24:{8}:channel,
    get=@32:{4}:button:[1:open 3:close 5:stop],
    get=@36:{4}:check,
    unique
}
