# Steffen Switch Transmitter, HS1527 based remote button

# should abort if (bb[0][0]!=0x00 || (bb[1][0]&0x07)!=0x07 || bb[1][0]!=bb[2][0] || bb[2][0]==bb[3][0])

decoder {
    name        = Steffen-Switch,
    modulation  = OOK_PPM,
    short       = 370,
    long        = 750,
    gap         = 1080,
    reset       = 6000,
    bits        = 25,
    get         = @0:{5}:code,
    get         = @20:{4}:button:[14:A 13:B 11:C 7:D 15:ALL],
    get         = @16:{4}:state:[15:OFF 0:ON],
}
