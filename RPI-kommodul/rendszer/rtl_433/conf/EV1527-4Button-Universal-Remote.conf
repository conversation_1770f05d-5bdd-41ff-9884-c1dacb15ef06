# EV1527 4-Button Universal remote
#
# This decoder reads button pressed from a EV1527 based remote control.
# The four rubber buttons are directly connected to the EV1527 data pins.
# Therefore, pressing more than one button at the same time is possible.
# The flex spec will match this and output a string containing the pressed
# button combination e.g.
# " code : REMOTE-B      button : AC "
# if button A and C have been pressed simultaneously
# All possible combinations are matched for completeness, while not all
# are are useful because of the size of the remote.
# You can simply not press easily more than two buttons at the same time ;-)
# 
# The shown example can distinguish between multiple remotes (two in this case).
# If this is not desired, a simple "match=abcde" can limit the detection of
# one unique EV1527 code.

decoder {
        n=EV1527-Remote,
        m=OOK_PWM,
        s=369,
        l=1072,
        g=1400,
        r=12840,
        bits>=24,
        repeats>=3,
        invert,
        get=@0:{20}:code:[123456:REMOTE-A 987654:REMOTE-B],
        get=@20:{4}:button:[1:A 2:B 3:AB 4:C 5:AC 6:BC 7:ABC 8:D 9:AD 10:BD 11:ABD 12:CD 13:ACD 14:BCD 15:ALL],
        unique
}
