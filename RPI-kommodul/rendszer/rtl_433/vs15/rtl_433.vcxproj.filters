﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Source Files\devices">
      <UniqueIdentifier>{f21dc9b7-0ca1-4db6-a4de-5256e2ee79a5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\getopt">
      <UniqueIdentifier>{a9d7c5bb-283f-4401-ab34-d061be0abbfc}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\abuf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\am_analyze.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\baseband.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\bitbuffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compat_alarm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compat_paths.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\compat_time.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\confparse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\data.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\data_tag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\decoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\decoder_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fatal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fileformat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\http_server.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\jsmn.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\list.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\mongoose.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\optparse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\output_influx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\output_mqtt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pulse_analyzer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pulse_demod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pulse_detect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\pulse_detect_fsk.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\r_api.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\r_device.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\r_private.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\r_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\rfraw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\rtl_433.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\rtl_433_devices.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\samp_grab.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\sdr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\term_ctl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\write_sigrok.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\getopt\getopt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\abuf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\am_analyze.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\baseband.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\bitbuffer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\compat_alarm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\compat_paths.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\compat_time.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\confparse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\data.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\data_tag.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\decoder_util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\fileformat.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\http_server.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\jsmn.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\mongoose.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\optparse.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\output_influx.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\output_mqtt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pulse_analyzer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pulse_demod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pulse_detect.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\pulse_detect_fsk.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\r_api.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\r_util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\rfraw.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\rtl_433.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\samp_grab.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\sdr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\term_ctl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\write_sigrok.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\getopt\getopt.c">
      <Filter>Source Files\getopt</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\abmt.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\acurite.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\akhan_100F14.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\alecto.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ambient_weather.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ambientweather_tx8300.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ambientweather_wh31e.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\archos_tbh.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\auriol_aft77b2.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\auriol_afw2a1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\auriol_ahfl.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\auriol_hg02832.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\blueline.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\blyss.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\brennenstuhl_rcs_2044.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\bresser_3ch.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\bresser_5in1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\bresser_6in1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\bresser_7in1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\bt_rain.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\burnhardbbq.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\calibeur.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\cardin.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\cavius.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\chuango.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\companion_wtr001.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\cotech_36_7959.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\current_cost.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\danfoss.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\digitech_xc0324.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\directv.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\dish_remote_6_3.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\dsc.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ecodhome.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ecowitt.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\efergy_e2_classic.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\efergy_optical.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\efth800.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\elro_db286a.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\elv.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\emontx.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ert_idm.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ert_scm.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\esa.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\esic_emt7110.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\esperanza_ews.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\eurochron.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\fineoffset.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\fineoffset_wh1050.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\fineoffset_wh1080.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\fineoffset_wh31l.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\flex.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\fordremote.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\fs20.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ft004b.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ge_coloreffects.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\generic_motion.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\generic_remote.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\generic_temperature_sensor.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\gt_tmbbq05.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\gt_wt_02.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\gt_wt_03.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\hcs200.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\hideki.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\holman_ws5029.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\hondaremote.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\honeywell.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\honeywell_cm921.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\honeywell_wdb.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ht680.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ibis_beacon.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ikea_sparsnas.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\infactory.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\inovalley-kw9015b.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\insteon.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\interlogix.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\intertechno.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\kedsum.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\kerui.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\klimalogg.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrosse.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrosse_breezepro.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrosse_r1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrosse_th3.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrosse_tx141x.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrosse_tx35.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrosse_wr1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrosse_ws7000.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lacrossews.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\lightwave_rf.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\m_bus.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\marlec_solar.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\maverick_et73.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\maverick_et73x.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\mebus.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\missil_ml0757.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\new_template.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\newkaku.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\nexa.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\nexus.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\nice_flor_s.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\norgo.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\oil_standard.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\oil_watchman.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\opus_xt300.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\oregon_scientific.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\oregon_scientific_sl109h.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\oregon_scientific_v1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\philips_aj3650.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\philips_aj7010.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\prologue.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\proove.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\quhwa.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\radiohead_ask.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\rftech.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\rojaflex.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\rubicson.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\rubicson_48659.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\s3318p.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\schraeder.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\scmplus.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\secplus_v1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\secplus_v2.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\sharp_spc775.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\silvercrest.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\simplisafe.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\smoke_gs558.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\solight_te44.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\somfy_iohc.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\somfy_rts.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\springfield.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\steelmate.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tfa_30_3196.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tfa_30_3221.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tfa_drop_30.3233.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tfa_marbella.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tfa_pool_thermometer.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tfa_twin_plus_30.3049.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\thermopro_tp11.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\thermopro_tp12.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\thermopro_tx2.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_abarth124.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_citroen.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_elantra2012.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_ford.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_hyundai_vdo.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_jansite.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_jansite_solar.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_pmv107j.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_renault.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\tpms_toyota.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ts_ft002.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ttx201.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\vaillant_vrt340f.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\visonic_powercode.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\waveman.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\wg_pb12v1.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\ws2032.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\wssensor.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\wt0124.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\wt450.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\x10_rf.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
    <ClCompile Include="..\src\devices\x10_sec.c">
      <Filter>Source Files\devices</Filter>
    </ClCompile>
  </ItemGroup>
</Project>
