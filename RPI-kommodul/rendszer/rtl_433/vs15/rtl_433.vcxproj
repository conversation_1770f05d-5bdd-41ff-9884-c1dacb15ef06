﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7EB4B17A-2185-47A5-8170-ACDC7EA30515}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>rtl_433</RootNamespace>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>RTLSDR;_USE_MATH_DEFINES;_CRT_SECURE_NO_WARNINGS;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\include\;..\src\;..\..\rtl-sdr\;..\..\libusb\include\libusb-1.0</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\libusb\MS64\dll;..\..\rtl-sdr\x64\</AdditionalLibraryDirectories>
      <AdditionalDependencies>ws2_32.lib;rtlsdr.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;libusb-1.0.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>COPY ..\..\rtl-sdr\x64\rtlsdr.dll $(TargetDir)
COPY ..\..\libusb\MS64\dll\libusb*.dll $(TargetDir)</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>RTLSDR;_USE_MATH_DEFINES;_CRT_SECURE_NO_WARNINGS;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\include\;..\src\;..\..\rtl-sdr\;..\..\libusb\include\libusb-1.0</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>..\..\libusb\MS64\dll;..\..\rtl-sdr\x64\</AdditionalLibraryDirectories>
      <AdditionalDependencies>ws2_32.lib;rtlsdr.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;libusb-1.0.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>COPY ..\..\rtl-sdr\x64\rtlsdr.dll $(TargetDir)
COPY ..\..\libusb\MS64\dll\libusb*.dll $(TargetDir)</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\include\abuf.h" />
    <ClInclude Include="..\include\am_analyze.h" />
    <ClInclude Include="..\include\baseband.h" />
    <ClInclude Include="..\include\bitbuffer.h" />
    <ClInclude Include="..\include\compat_alarm.h" />
    <ClInclude Include="..\include\compat_paths.h" />
    <ClInclude Include="..\include\compat_time.h" />
    <ClInclude Include="..\include\confparse.h" />
    <ClInclude Include="..\include\data.h" />
    <ClInclude Include="..\include\data_tag.h" />
    <ClInclude Include="..\include\decoder.h" />
    <ClInclude Include="..\include\decoder_util.h" />
    <ClInclude Include="..\include\fatal.h" />
    <ClInclude Include="..\include\fileformat.h" />
    <ClInclude Include="..\include\http_server.h" />
    <ClInclude Include="..\include\jsmn.h" />
    <ClInclude Include="..\include\list.h" />
    <ClInclude Include="..\include\mongoose.h" />
    <ClInclude Include="..\include\optparse.h" />
    <ClInclude Include="..\include\output_influx.h" />
    <ClInclude Include="..\include\output_mqtt.h" />
    <ClInclude Include="..\include\pulse_analyzer.h" />
    <ClInclude Include="..\include\pulse_demod.h" />
    <ClInclude Include="..\include\pulse_detect.h" />
    <ClInclude Include="..\include\pulse_detect_fsk.h" />
    <ClInclude Include="..\include\r_api.h" />
    <ClInclude Include="..\include\r_device.h" />
    <ClInclude Include="..\include\r_private.h" />
    <ClInclude Include="..\include\r_util.h" />
    <ClInclude Include="..\include\rfraw.h" />
    <ClInclude Include="..\include\rtl_433.h" />
    <ClInclude Include="..\include\rtl_433_devices.h" />
    <ClInclude Include="..\include\samp_grab.h" />
    <ClInclude Include="..\include\sdr.h" />
    <ClInclude Include="..\include\term_ctl.h" />
    <ClInclude Include="..\include\util.h" />
    <ClInclude Include="..\include\write_sigrok.h" />
    <ClInclude Include="..\src\getopt\getopt.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\abuf.c" />
    <ClCompile Include="..\src\am_analyze.c" />
    <ClCompile Include="..\src\baseband.c" />
    <ClCompile Include="..\src\bitbuffer.c" />
    <ClCompile Include="..\src\compat_alarm.c" />
    <ClCompile Include="..\src\compat_paths.c" />
    <ClCompile Include="..\src\compat_time.c" />
    <ClCompile Include="..\src\confparse.c" />
    <ClCompile Include="..\src\data.c" />
    <ClCompile Include="..\src\data_tag.c" />
    <ClCompile Include="..\src\decoder_util.c" />
    <ClCompile Include="..\src\fileformat.c" />
    <ClCompile Include="..\src\http_server.c" />
    <ClCompile Include="..\src\jsmn.c" />
    <ClCompile Include="..\src\list.c" />
    <ClCompile Include="..\src\mongoose.c" />
    <ClCompile Include="..\src\optparse.c" />
    <ClCompile Include="..\src\output_influx.c" />
    <ClCompile Include="..\src\output_mqtt.c" />
    <ClCompile Include="..\src\pulse_analyzer.c" />
    <ClCompile Include="..\src\pulse_demod.c" />
    <ClCompile Include="..\src\pulse_detect.c" />
    <ClCompile Include="..\src\pulse_detect_fsk.c" />
    <ClCompile Include="..\src\r_api.c" />
    <ClCompile Include="..\src\r_util.c" />
    <ClCompile Include="..\src\rfraw.c" />
    <ClCompile Include="..\src\rtl_433.c" />
    <ClCompile Include="..\src\samp_grab.c" />
    <ClCompile Include="..\src\sdr.c" />
    <ClCompile Include="..\src\term_ctl.c" />
    <ClCompile Include="..\src\util.c" />
    <ClCompile Include="..\src\write_sigrok.c" />
    <ClCompile Include="..\src\devices\abmt.c" />
    <ClCompile Include="..\src\devices\acurite.c" />
    <ClCompile Include="..\src\devices\akhan_100F14.c" />
    <ClCompile Include="..\src\devices\alecto.c" />
    <ClCompile Include="..\src\devices\ambient_weather.c" />
    <ClCompile Include="..\src\devices\ambientweather_tx8300.c" />
    <ClCompile Include="..\src\devices\ambientweather_wh31e.c" />
    <ClCompile Include="..\src\devices\archos_tbh.c" />
    <ClCompile Include="..\src\devices\auriol_aft77b2.c" />
    <ClCompile Include="..\src\devices\auriol_afw2a1.c" />
    <ClCompile Include="..\src\devices\auriol_ahfl.c" />
    <ClCompile Include="..\src\devices\auriol_hg02832.c" />
    <ClCompile Include="..\src\devices\blueline.c" />
    <ClCompile Include="..\src\devices\blyss.c" />
    <ClCompile Include="..\src\devices\brennenstuhl_rcs_2044.c" />
    <ClCompile Include="..\src\devices\bresser_3ch.c" />
    <ClCompile Include="..\src\devices\bresser_5in1.c" />
    <ClCompile Include="..\src\devices\bresser_6in1.c" />
    <ClCompile Include="..\src\devices\bresser_7in1.c" />
    <ClCompile Include="..\src\devices\bt_rain.c" />
    <ClCompile Include="..\src\devices\burnhardbbq.c" />
    <ClCompile Include="..\src\devices\calibeur.c" />
    <ClCompile Include="..\src\devices\cardin.c" />
    <ClCompile Include="..\src\devices\cavius.c" />
    <ClCompile Include="..\src\devices\chuango.c" />
    <ClCompile Include="..\src\devices\companion_wtr001.c" />
    <ClCompile Include="..\src\devices\cotech_36_7959.c" />
    <ClCompile Include="..\src\devices\current_cost.c" />
    <ClCompile Include="..\src\devices\danfoss.c" />
    <ClCompile Include="..\src\devices\digitech_xc0324.c" />
    <ClCompile Include="..\src\devices\directv.c" />
    <ClCompile Include="..\src\devices\dish_remote_6_3.c" />
    <ClCompile Include="..\src\devices\dsc.c" />
    <ClCompile Include="..\src\devices\ecodhome.c" />
    <ClCompile Include="..\src\devices\ecowitt.c" />
    <ClCompile Include="..\src\devices\efergy_e2_classic.c" />
    <ClCompile Include="..\src\devices\efergy_optical.c" />
    <ClCompile Include="..\src\devices\efth800.c" />
    <ClCompile Include="..\src\devices\elro_db286a.c" />
    <ClCompile Include="..\src\devices\elv.c" />
    <ClCompile Include="..\src\devices\emontx.c" />
    <ClCompile Include="..\src\devices\ert_idm.c" />
    <ClCompile Include="..\src\devices\ert_scm.c" />
    <ClCompile Include="..\src\devices\esa.c" />
    <ClCompile Include="..\src\devices\esic_emt7110.c" />
    <ClCompile Include="..\src\devices\esperanza_ews.c" />
    <ClCompile Include="..\src\devices\eurochron.c" />
    <ClCompile Include="..\src\devices\fineoffset.c" />
    <ClCompile Include="..\src\devices\fineoffset_wh1050.c" />
    <ClCompile Include="..\src\devices\fineoffset_wh1080.c" />
    <ClCompile Include="..\src\devices\fineoffset_wh31l.c" />
    <ClCompile Include="..\src\devices\flex.c" />
    <ClCompile Include="..\src\devices\fordremote.c" />
    <ClCompile Include="..\src\devices\fs20.c" />
    <ClCompile Include="..\src\devices\ft004b.c" />
    <ClCompile Include="..\src\devices\ge_coloreffects.c" />
    <ClCompile Include="..\src\devices\generic_motion.c" />
    <ClCompile Include="..\src\devices\generic_remote.c" />
    <ClCompile Include="..\src\devices\generic_temperature_sensor.c" />
    <ClCompile Include="..\src\devices\gt_tmbbq05.c" />
    <ClCompile Include="..\src\devices\gt_wt_02.c" />
    <ClCompile Include="..\src\devices\gt_wt_03.c" />
    <ClCompile Include="..\src\devices\hcs200.c" />
    <ClCompile Include="..\src\devices\hideki.c" />
    <ClCompile Include="..\src\devices\holman_ws5029.c" />
    <ClCompile Include="..\src\devices\hondaremote.c" />
    <ClCompile Include="..\src\devices\honeywell.c" />
    <ClCompile Include="..\src\devices\honeywell_cm921.c" />
    <ClCompile Include="..\src\devices\honeywell_wdb.c" />
    <ClCompile Include="..\src\devices\ht680.c" />
    <ClCompile Include="..\src\devices\ibis_beacon.c" />
    <ClCompile Include="..\src\devices\ikea_sparsnas.c" />
    <ClCompile Include="..\src\devices\infactory.c" />
    <ClCompile Include="..\src\devices\inovalley-kw9015b.c" />
    <ClCompile Include="..\src\devices\insteon.c" />
    <ClCompile Include="..\src\devices\interlogix.c" />
    <ClCompile Include="..\src\devices\intertechno.c" />
    <ClCompile Include="..\src\devices\kedsum.c" />
    <ClCompile Include="..\src\devices\kerui.c" />
    <ClCompile Include="..\src\devices\klimalogg.c" />
    <ClCompile Include="..\src\devices\lacrosse.c" />
    <ClCompile Include="..\src\devices\lacrosse_breezepro.c" />
    <ClCompile Include="..\src\devices\lacrosse_r1.c" />
    <ClCompile Include="..\src\devices\lacrosse_th3.c" />
    <ClCompile Include="..\src\devices\lacrosse_tx141x.c" />
    <ClCompile Include="..\src\devices\lacrosse_tx35.c" />
    <ClCompile Include="..\src\devices\lacrosse_wr1.c" />
    <ClCompile Include="..\src\devices\lacrosse_ws7000.c" />
    <ClCompile Include="..\src\devices\lacrossews.c" />
    <ClCompile Include="..\src\devices\lightwave_rf.c" />
    <ClCompile Include="..\src\devices\m_bus.c" />
    <ClCompile Include="..\src\devices\marlec_solar.c" />
    <ClCompile Include="..\src\devices\maverick_et73.c" />
    <ClCompile Include="..\src\devices\maverick_et73x.c" />
    <ClCompile Include="..\src\devices\mebus.c" />
    <ClCompile Include="..\src\devices\missil_ml0757.c" />
    <ClCompile Include="..\src\devices\new_template.c" />
    <ClCompile Include="..\src\devices\newkaku.c" />
    <ClCompile Include="..\src\devices\nexa.c" />
    <ClCompile Include="..\src\devices\nexus.c" />
    <ClCompile Include="..\src\devices\nice_flor_s.c" />
    <ClCompile Include="..\src\devices\norgo.c" />
    <ClCompile Include="..\src\devices\oil_standard.c" />
    <ClCompile Include="..\src\devices\oil_watchman.c" />
    <ClCompile Include="..\src\devices\opus_xt300.c" />
    <ClCompile Include="..\src\devices\oregon_scientific.c" />
    <ClCompile Include="..\src\devices\oregon_scientific_sl109h.c" />
    <ClCompile Include="..\src\devices\oregon_scientific_v1.c" />
    <ClCompile Include="..\src\devices\philips_aj3650.c" />
    <ClCompile Include="..\src\devices\philips_aj7010.c" />
    <ClCompile Include="..\src\devices\prologue.c" />
    <ClCompile Include="..\src\devices\proove.c" />
    <ClCompile Include="..\src\devices\quhwa.c" />
    <ClCompile Include="..\src\devices\radiohead_ask.c" />
    <ClCompile Include="..\src\devices\rftech.c" />
    <ClCompile Include="..\src\devices\rojaflex.c" />
    <ClCompile Include="..\src\devices\rubicson.c" />
    <ClCompile Include="..\src\devices\rubicson_48659.c" />
    <ClCompile Include="..\src\devices\s3318p.c" />
    <ClCompile Include="..\src\devices\schraeder.c" />
    <ClCompile Include="..\src\devices\scmplus.c" />
    <ClCompile Include="..\src\devices\secplus_v1.c" />
    <ClCompile Include="..\src\devices\secplus_v2.c" />
    <ClCompile Include="..\src\devices\sharp_spc775.c" />
    <ClCompile Include="..\src\devices\silvercrest.c" />
    <ClCompile Include="..\src\devices\simplisafe.c" />
    <ClCompile Include="..\src\devices\smoke_gs558.c" />
    <ClCompile Include="..\src\devices\solight_te44.c" />
    <ClCompile Include="..\src\devices\somfy_iohc.c" />
    <ClCompile Include="..\src\devices\somfy_rts.c" />
    <ClCompile Include="..\src\devices\springfield.c" />
    <ClCompile Include="..\src\devices\steelmate.c" />
    <ClCompile Include="..\src\devices\tfa_30_3196.c" />
    <ClCompile Include="..\src\devices\tfa_30_3221.c" />
    <ClCompile Include="..\src\devices\tfa_drop_30.3233.c" />
    <ClCompile Include="..\src\devices\tfa_marbella.c" />
    <ClCompile Include="..\src\devices\tfa_pool_thermometer.c" />
    <ClCompile Include="..\src\devices\tfa_twin_plus_30.3049.c" />
    <ClCompile Include="..\src\devices\thermopro_tp11.c" />
    <ClCompile Include="..\src\devices\thermopro_tp12.c" />
    <ClCompile Include="..\src\devices\thermopro_tx2.c" />
    <ClCompile Include="..\src\devices\tpms_abarth124.c" />
    <ClCompile Include="..\src\devices\tpms_citroen.c" />
    <ClCompile Include="..\src\devices\tpms_elantra2012.c" />
    <ClCompile Include="..\src\devices\tpms_ford.c" />
    <ClCompile Include="..\src\devices\tpms_hyundai_vdo.c" />
    <ClCompile Include="..\src\devices\tpms_jansite.c" />
    <ClCompile Include="..\src\devices\tpms_jansite_solar.c" />
    <ClCompile Include="..\src\devices\tpms_pmv107j.c" />
    <ClCompile Include="..\src\devices\tpms_renault.c" />
    <ClCompile Include="..\src\devices\tpms_toyota.c" />
    <ClCompile Include="..\src\devices\ts_ft002.c" />
    <ClCompile Include="..\src\devices\ttx201.c" />
    <ClCompile Include="..\src\devices\vaillant_vrt340f.c" />
    <ClCompile Include="..\src\devices\visonic_powercode.c" />
    <ClCompile Include="..\src\devices\waveman.c" />
    <ClCompile Include="..\src\devices\wg_pb12v1.c" />
    <ClCompile Include="..\src\devices\ws2032.c" />
    <ClCompile Include="..\src\devices\wssensor.c" />
    <ClCompile Include="..\src\devices\wt0124.c" />
    <ClCompile Include="..\src\devices\wt450.c" />
    <ClCompile Include="..\src\devices\x10_rf.c" />
    <ClCompile Include="..\src\devices\x10_sec.c" />
    <ClCompile Include="..\src\getopt\getopt.c" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
