########################################################################
# Build libraries and executables
########################################################################
# commodity libraries
# consider -fvisibility=hidden
# Proper object library type was only introduced with CMake 2.8.8
add_library(r_433 STATIC
    abuf.c
    am_analyze.c
    baseband.c
    bitbuffer.c
    compat_alarm.c
    compat_paths.c
    compat_time.c
    confparse.c
    data.c
    data_tag.c
    decoder_util.c
    fileformat.c
    http_server.c
    jsmn.c
    list.c
    mongoose.c
    optparse.c
    output_influx.c
    output_mqtt.c
    pulse_analyzer.c
    pulse_demod.c
    pulse_detect.c
    pulse_detect_fsk.c
    r_api.c
    r_util.c
    rfraw.c
    samp_grab.c
    sdr.c
    term_ctl.c
    util.c
    write_sigrok.c
    devices/abmt.c
    devices/acurite.c
    devices/akhan_100F14.c
    devices/alecto.c
    devices/ambient_weather.c
    devices/ambientweather_tx8300.c
    devices/ambientweather_wh31e.c
    devices/archos_tbh.c
    devices/auriol_aft77b2.c
    devices/auriol_afw2a1.c
    devices/auriol_ahfl.c
    devices/auriol_hg02832.c
    devices/blueline.c
    devices/blyss.c
    devices/brennenstuhl_rcs_2044.c
    devices/bresser_3ch.c
    devices/bresser_5in1.c
    devices/bresser_6in1.c
    devices/bresser_7in1.c
    devices/bt_rain.c
    devices/burnhardbbq.c
    devices/calibeur.c
    devices/cardin.c
    devices/cavius.c
    devices/chuango.c
    devices/companion_wtr001.c
    devices/cotech_36_7959.c
    devices/current_cost.c
    devices/danfoss.c
    devices/digitech_xc0324.c
    devices/directv.c
    devices/dish_remote_6_3.c
    devices/dsc.c
    devices/ecodhome.c
    devices/ecowitt.c
    devices/efergy_e2_classic.c
    devices/efergy_optical.c
    devices/efth800.c
    devices/elro_db286a.c
    devices/elv.c
    devices/emontx.c
    devices/ert_idm.c
    devices/ert_scm.c
    devices/esa.c
    devices/esic_emt7110.c
    devices/esperanza_ews.c
    devices/eurochron.c
    devices/fineoffset.c
    devices/fineoffset_wh1050.c
    devices/fineoffset_wh1080.c
    devices/fineoffset_wh31l.c
    devices/flex.c
    devices/fordremote.c
    devices/fs20.c
    devices/ft004b.c
    devices/ge_coloreffects.c
    devices/generic_motion.c
    devices/generic_remote.c
    devices/generic_temperature_sensor.c
    devices/gt_tmbbq05.c
    devices/gt_wt_02.c
    devices/gt_wt_03.c
    devices/hcs200.c
    devices/hideki.c
    devices/holman_ws5029.c
    devices/hondaremote.c
    devices/honeywell.c
    devices/honeywell_cm921.c
    devices/honeywell_wdb.c
    devices/ht680.c
    devices/ibis_beacon.c
    devices/ikea_sparsnas.c
    devices/infactory.c
    devices/inovalley-kw9015b.c
    devices/insteon.c
    devices/interlogix.c
    devices/intertechno.c
    devices/kedsum.c
    devices/kerui.c
    devices/klimalogg.c
    devices/lacrosse.c
    devices/lacrosse_breezepro.c
    devices/lacrosse_r1.c
    devices/lacrosse_th3.c
    devices/lacrosse_tx141x.c
    devices/lacrosse_tx35.c
    devices/lacrosse_wr1.c
    devices/lacrosse_ws7000.c
    devices/lacrossews.c
    devices/lightwave_rf.c
    devices/m_bus.c
    devices/marlec_solar.c
    devices/maverick_et73.c
    devices/maverick_et73x.c
    devices/mebus.c
    devices/missil_ml0757.c
    devices/new_template.c
    devices/newkaku.c
    devices/nexa.c
    devices/nexus.c
    devices/nice_flor_s.c
    devices/norgo.c
    devices/oil_standard.c
    devices/oil_watchman.c
    devices/opus_xt300.c
    devices/oregon_scientific.c
    devices/oregon_scientific_sl109h.c
    devices/oregon_scientific_v1.c
    devices/philips_aj3650.c
    devices/philips_aj7010.c
    devices/prologue.c
    devices/proove.c
    devices/quhwa.c
    devices/radiohead_ask.c
    devices/rftech.c
    devices/rojaflex.c
    devices/rubicson.c
    devices/rubicson_48659.c
    devices/s3318p.c
    devices/schraeder.c
    devices/scmplus.c
    devices/secplus_v1.c
    devices/secplus_v2.c
    devices/sharp_spc775.c
    devices/silvercrest.c
    devices/simplisafe.c
    devices/smoke_gs558.c
    devices/solight_te44.c
    devices/somfy_iohc.c
    devices/somfy_rts.c
    devices/springfield.c
    devices/steelmate.c
    devices/tfa_30_3196.c
    devices/tfa_30_3221.c
    devices/tfa_drop_30.3233.c
    devices/tfa_marbella.c
    devices/tfa_pool_thermometer.c
    devices/tfa_twin_plus_30.3049.c
    devices/thermopro_tp11.c
    devices/thermopro_tp12.c
    devices/thermopro_tx2.c
    devices/tpms_abarth124.c
    devices/tpms_citroen.c
    devices/tpms_elantra2012.c
    devices/tpms_ford.c
    devices/tpms_hyundai_vdo.c
    devices/tpms_jansite.c
    devices/tpms_jansite_solar.c
    devices/tpms_pmv107j.c
    devices/tpms_renault.c
    devices/tpms_toyota.c
    devices/ts_ft002.c
    devices/ttx201.c
    devices/vaillant_vrt340f.c
    devices/visonic_powercode.c
    devices/waveman.c
    devices/wg_pb12v1.c
    devices/ws2032.c
    devices/wssensor.c
    devices/wt0124.c
    devices/wt450.c
    devices/x10_rf.c
    devices/x10_sec.c
)

if("${CMAKE_C_COMPILER_ID}" STREQUAL "GNU")
    set_source_files_properties(mongoose.c PROPERTIES COMPILE_FLAGS "-Wno-format -Wno-format-security")
endif()
if("${CMAKE_C_COMPILER_ID}" MATCHES "Clang")
    set_source_files_properties(mongoose.c PROPERTIES COMPILE_FLAGS "-Wno-format-pedantic -Wno-format-security -Wno-large-by-value-copy")
endif()

add_executable(rtl_433 rtl_433.c)
target_link_libraries(rtl_433 r_433)

if(MSVC)
    # needs CMake 3.1 but Windows builds should have that
    target_sources(rtl_433 PRIVATE getopt/getopt.c)
endif()

add_library(data data.c abuf.c term_ctl.c)
target_link_libraries(data ${NET_LIBRARIES})

target_link_libraries(rtl_433
    ${SDR_LIBRARIES}
    ${NET_LIBRARIES}
)

set(INSTALL_TARGETS rtl_433)
if(UNIX)
target_link_libraries(rtl_433 m)
endif()

# Explicitly say that we want C99
set_target_properties(rtl_433 r_433 PROPERTIES C_STANDARD 99)

########################################################################
# Install built library files & utilities
########################################################################
install(TARGETS ${INSTALL_TARGETS}
    RUNTIME DESTINATION bin              # .dll file
)
