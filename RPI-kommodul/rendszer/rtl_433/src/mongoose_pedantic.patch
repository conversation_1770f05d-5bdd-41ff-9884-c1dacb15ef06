diff --git a/include/mongoose.h b/include/mongoose.h
index 03cc04e..b885270 100644
--- a/include/mongoose.h
+++ b/include/mongoose.h
@@ -1,3 +1,6 @@
+#ifndef INCLUDE_MONGOOSE_H_
+#define INCLUDE_MONGOOSE_H_
+
 #ifdef MG_MODULE_LINES
 #line 1 "mongoose/src/mg_common.h"
 #endif
@@ -6750,3 +6753,5 @@ struct mg_iface *mg_socks_mk_iface(struct mg_mgr *, const char *proxy_addr);
 
 #endif
 #endif
+
+#endif /* INCLUDE_MONGOOSE_H_ */
diff --git a/include/mongoose.h b/include/mongoose.h
index 316f5f9..03cc04e 100644
--- a/include/mongoose.h
+++ b/include/mongoose.h
@@ -352,10 +352,8 @@ unsigned int sleep(unsigned int seconds);
 /* https://stackoverflow.com/questions/16647819/timegm-cross-platform */
 #define timegm _mkgmtime
 
-#define gmtime_r(a, b) \
-  do {                 \
-    *(b) = *gmtime(a); \
-  } while (0)
+#define gmtime_r(a, b) (gmtime_s((b), (a)), (b))
+#define localtime_r(a, b) (localtime_s((b), (a)), (b))
 
 #endif /* CS_PLATFORM == CS_P_WINDOWS */
 #endif /* CS_COMMON_PLATFORMS_PLATFORM_WINDOWS_H_ */
diff --git a/src/mongoose.c b/src/mongoose.c
index fe0df63..160df87 100644
--- a/src/mongoose.c
+++ b/src/mongoose.c
@@ -4330,19 +4330,27 @@ int mg_socketpair(sock_t sp[2], int sock_type) {
   sa2 = sa;
 
   if ((sock = socket(AF_INET, sock_type, 0)) == INVALID_SOCKET) {
+    // abort
   } else if (bind(sock, &sa.sa, len) != 0) {
+    // abort
   } else if (sock_type == SOCK_STREAM && listen(sock, 1) != 0) {
+    // abort
   } else if (getsockname(sock, &sa.sa, &len) != 0) {
+    // abort
   } else if ((sp[0] = socket(AF_INET, sock_type, 0)) == INVALID_SOCKET) {
+    // abort
   } else if (sock_type == SOCK_STREAM && connect(sp[0], &sa.sa, len) != 0) {
+    // abort
   } else if (sock_type == SOCK_DGRAM &&
              (bind(sp[0], &sa2.sa, len) != 0 ||
               getsockname(sp[0], &sa2.sa, &len) != 0 ||
               connect(sp[0], &sa.sa, len) != 0 ||
               connect(sock, &sa2.sa, len) != 0)) {
+    // abort
   } else if ((sp[1] = (sock_type == SOCK_DGRAM ? sock : mg_socketpair_accept(
                                                             sock, &sa, len))) ==
              INVALID_SOCKET) {
+    // abort
   } else {
     mg_set_close_on_exec(sp[0]);
     mg_set_close_on_exec(sp[1]);
@@ -7271,7 +7279,8 @@ static void mg_http_construct_etag(char *buf, size_t buf_len,
 
 #ifndef WINCE
 static void mg_gmt_time_string(char *buf, size_t buf_len, time_t *t) {
-  strftime(buf, buf_len, "%a, %d %b %Y %H:%M:%S GMT", gmtime(t));
+  struct tm tm;
+  strftime(buf, buf_len, "%a, %d %b %Y %H:%M:%S GMT", gmtime_r(t, &tm));
 }
 #else
 /* Look wince_lib.c for WindowsCE implementation */
@@ -7886,6 +7895,7 @@ static void mg_print_dir_entry(struct mg_connection *nc, const char *file_name,
   int is_dir = S_ISDIR(stp->st_mode);
   const char *slash = is_dir ? "/" : "";
   struct mg_str href;
+  struct tm tm;
 
   if (is_dir) {
     snprintf(size, sizeof(size), "%s", "[DIRECTORY]");
@@ -7904,7 +7914,7 @@ static void mg_print_dir_entry(struct mg_connection *nc, const char *file_name,
       snprintf(size, sizeof(size), "%.1fG", (double) fsize / 1073741824);
     }
   }
-  strftime(mod, sizeof(mod), "%d-%b-%Y %H:%M", localtime(&stp->st_mtime));
+  strftime(mod, sizeof(mod), "%d-%b-%Y %H:%M", localtime_r(&stp->st_mtime, &tm));
   mg_escape(file_name, path, sizeof(path));
   href = mg_url_encode(mg_mk_str(file_name));
   mg_printf_http_chunk(nc,
