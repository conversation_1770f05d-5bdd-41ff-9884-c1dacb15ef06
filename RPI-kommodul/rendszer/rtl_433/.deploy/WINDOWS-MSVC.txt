# rtl_433 Windows MSVC build

For the SoapySDR builds you need PothosSDR installed https://downloads.myriadrf.org/builds/PothosSDR/
Any recent version should work, currently built with 2021.01.28-vc16:
https://downloads.myriadrf.org/builds/PothosSDR/PothosSDR-2021.05.01-vc16-x64.exe

For the TLS builds (mqtts and influxs) you need OpenSSL installed.
E.g. install Chocolatey https://chocolatey.org/install
then run `choco install openssl.light`

An alternative to installing SoapySDR from PothosSDR is to extract the installer
and copy the builds (.exe) from this release to the `bin` directory in PothosSDR.

This release includes the Microsoft Visual C++ Redistributable for Visual Studio 2015, 2017 and 2019.
S.a. https://support.microsoft.com/en-us/topic/the-latest-supported-visual-c-downloads-2647da03-1eea-4433-9aff-95f26a218cc0
