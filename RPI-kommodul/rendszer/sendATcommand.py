#!/usr/bin/python3
# -*- coding: utf-8 -*-

import logging.handlers
import sys
import time
import serial


mlog = logging.getLogger(__file__)
mlog.setLevel(logging.INFO)
handler = logging.handlers.SysLogHandler(address='/dev/log')
handler.setFormatter(logging.Formatter('sendATcommand.py[%(process)d]: %(levelname)s: %(message)s'))
mlog.addHandler(handler)


####################################################
#                                                  #
#       SIM7600 AT parancs kommunikáció            #
#        interface indításhoz,                     #
#        és reset-hez                              #
#                                                  #
####################################################
class Modem:

    def __init__(self, device):
        self._serialPort = device

    def __enter__(self):
        sp = serial.Serial()
        sp.port = self._serialPort
        sp.baudrate = 115200
        sp.timeout = 30
        sp.bytesize = serial.EIGHTBITS
        sp.parity = serial.PARITY_NONE
        sp.stopbits = serial.STOPBITS_ONE
        # USB-s csatlakozásnál hardware flow control kell
        sp.rtscts = True
        sp.dsrdtr = True
        sp.xonxoff = False
        try:
            sp.open()
        except Exception as e:
            mlog.error("error opening {}: {}".format(self._serialPort, str(e)))
            sys.exit(1)
        self._port = sp
        return self

    def __exit__(self, errortype, value, traceback):
        if self._port:
            self._port.close()

    def _send(self, cmd):
        if not self._port.isOpen():
            mlog.error('Modem port hiba: mégsincs nyitva küldésnél')
            sys.exit(2)
        self._port.reset_output_buffer()
        self._port.reset_input_buffer()
        # '\r\n' felel meg az ENTER billentyűnek
        mlog.info(cmd)
        self._port.write((cmd + '\r\n').encode())

    def _recieve(self, expectedString):
        if not self._port.isOpen():
            mlog.error('Modem port hiba: mégsincs nyitva fogadásnál')
            sys.exit(3)
        result = ''
        start_time = time.time()
        while expectedString not in result:
            # több sornyi üres semmit igyekszünk egyszerűsíteni
            rcv = self._port.read(self._port.in_waiting).decode('utf-8').strip('').replace('\r\n', '.')
            result += rcv
            if 'ERROR' in rcv or time.time() - start_time > 30:
                mlog.error('Modem hiba: {}'.format(rcv))
                return False
        return True

    def sendCommand(self, command, expectedResult):
        time.sleep(0.1)
        self._send(command)
        time.sleep(0.5)
        return self._recieve(expectedResult)


if __name__ == "__main__":

    if len(sys.argv) < 2:
        print('Használat:  sendATcommand.py dial|reset')
        sys.exit(0)

    option = sys.argv[1].lower()
    if option not in ['dial', 'reset']:
        print('Használat:  sendATcommand.py dial|reset')
        sys.exit(0)

    parancsok = {
        'dial': ('AT$QCRMCALL=1,1', 'OK'),
        'reset': ('AT+CRESET', 'OK'),
    }

    with Modem(device='/dev/ttyUSB2') as SIM7600:
        if not SIM7600.sendCommand(command=parancsok[option][0], expectedResult=parancsok[option][1]):
            sys.exit(1)

    sys.exit(0)
