#!/bin/bash

# Restart network interface if eth0 connection is down
# ----------------------------------------------------

IPADDR=$(/sbin/ifconfig | grep "inet 192.168")
if [ -z "$IPADDR" ]
then
  /usr/bin/logger "eth0 kapcsolat hiányzik! Újrainicializálás..."
  # ez már magában foglal egy reset-et is
  /sbin/ifdown --force eth0
  /usr/bin/logger "eth0 interface kilőve"
  /bin/sleep 3
  /usr/bin/logger "eth0 interface újra"
  /sbin/ifup eth0
  /usr/bin/logger "ifup eth0 exit code: $?"
  /bin/sleep 5

  IPADDR=$(/sbin/ifconfig | grep "inet 192.168")
  if [ -z "$IPADDR" ]
  then
    /usr/bin/logger "eth0 még mindig nem jó -> mi legyen?"
    echo "eth0 interface-t nem sikerült újrahú<PERSON>ni -> logot meg kell nézni"
  else
    /usr/bin/logger "eth0 kapcsolat OK!"
    PING="/bin/ping -q -c1 -W 10 -I eth0 ta120"
    # ha idáig eljutottunk, akkor nem nyomjuk el a kimenetet, hogy kapjunk az esetről e-mailt
    if ! ${PING}
    then
      /usr/bin/logger "Mellesleg a TA120 nem elérhető az eth0-on keresztül..."
    else
      /usr/bin/logger "TA120 kapcsolat OK!"
    fi
  fi
fi
