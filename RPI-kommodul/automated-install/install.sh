#!/bin/bash

echo "cloud-config.yml ékezet nélküliség ellenőrzés"
iconv -f UTF-8 -t ASCII cloud-config.yml > jokonfig.txt || echo "jokonfig.txt-t nézd meg!"
read -rsn1 -p"Mehet tovább?"
echo
rm jokonfig.txt

echo "cloud-config.yml szintaxis ellenőrzés"
python3 -c 'import yaml, sys; print(yaml.safe_load(sys.stdin))' < cloud-config.yml > /dev/null
read -rsn1 -p"Mehet tovább?"
echo

echo ".py fájlok szintaxis ellenőrzése..."
cd ..
python3 -m py_compile ./*.py || exit 1
read -rsn1 -p"Mehet tovább?"
rm -rf __pycache__
echo

echo "csomagkészítés .zip-be: "
vi VERSION
# a csomagnévnek ugyanannak kell lennie, mint a cloud-config.yml fájlban!
PACKAGE="ta120-client.zip"
FILES2INSTALL="
    rendszer/*
    common.py
    config.py
    daspidata.sdb
    devsensors.py
    logconfig.yml
    mptools/__init__.py
    mptools/base.py
    mptools/control.py
    mptools/workers.py
    recv-noisedata-mpq.py
    VERSION
    w_http.py
    w_ifmon.py
    w_mtndetect.py
    w_send.py
    w_status.py
"
rm ${PACKAGE}
# shellcheck disable=SC2086
zip ${PACKAGE} ${FILES2INSTALL}
echo "${PACKAGE}.zip"

# ---------------------------------------------------------------------------------
# TODO: ha a hypriot fejlesztése leáll, akkor lehet hogy ezzel érdemes továbbmenni:
#   https://github.com/debian-pi/raspbian-ua-netinst
#   vagy esetleg: https://github.com/RPi-Distro/pi-gen
# ---------------------------------------------------------------------------------

cd automated-install || exit 3
#echo "10 legfrissebb elérhető HypriotOS Linux verzió:"
#VLIST=$(curl -s https://blog.hypriot.com/downloads/ | grep ".img.zip" | head -10 | cut -d"/" -f8)
#echo "${VLIST}"
#VERSION=$(echo "${VLIST}" | head -1)
#echo -n "Ezek alapján a legfrissebb verzió: ${VERSION}. ENTER: ez maradjon, vagy írj egy konkrét verziószámot: "
#read -r valami
#if [ -n "$valami" ]
#then
#    VERSION=$valami
#fi
#
#BASEURL="https://github.com/hypriot/image-builder-rpi/releases/download"
#./flash \
#    --bootconf bootconf.txt \
#    --userdata cloud-config.yml \
#    --file ../${PACKAGE}  \
#    ${BASEURL}/"${VERSION}"/hypriotos-rpi-"${VERSION}".img.zip

IMGURL="https://github.com/jsiebens/rpi-cloud-init/releases/download/latest/rpi-cloud-init-raspios-bullseye-armhf.zip"
./flash \
    --bootconf bootconf.txt \
    --userdata cloud-config.yml \
    --file ../${PACKAGE}  \
    ${IMGURL}
