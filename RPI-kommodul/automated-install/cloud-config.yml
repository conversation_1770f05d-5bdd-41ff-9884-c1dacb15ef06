#cloud-config
# vim: syntax=yaml

# FIGYELEM: EKEZETES KARAKTEREK NEM HASZNALHATOAK A FAJLBAN!!!

# Set your hostname here, the manage_etc_hosts will update the hosts file entries as well
hostname: daspi64-t17
manage_etc_hosts: localhost

apt:
  preserve_sources_list: true

resize_rootfs: true
growpart:
    mode: auto
    devices: ["/"]
    ignore_growroot_disabled: false

locale: "en_US.UTF-8"
timezone: "Europe/Budapest"

users:
  - name: admin
    gecos: "IMAGMERNOK admin"
    primary-group: users
    shell: /bin/bash
    sudo:
      - ALL=(ALL) PASSWD:ALL
      - ALL=NOPASSWD:/sbin/reboot
      - ALL=NOPASSWD:/bin/ping
      - ALL=NOPASSWD:/sbin/ifup
      - ALL=NOPASSWD:/sbin/ifdown
    groups: users,docker,adm,dialout,audio,plugdev,netdev,video,i2c,kmem
    ssh-import-id: None
    lock_passwd: false
    # generalas: # openssl passwd -1 -salt 5p2bJZT jelszo
    passwd: $1$5p2bJZT$nNYmhHHfL.v7eUzmQ/cnA.
    ssh-authorized-keys:
      - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIFzmq0VyTcS5l8M8DcCHsBnkwsFq1l8wYpeLCTunaKJU fero@ferolaptop
      - ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDRUqmjdvicJRYczh4EDQ8tzBJ//QaQ+BKNYEWSe2eQjS3rvnY4moYxuGKLxbGAGAahBWNNVzxNYVbXkFXwxN6jUg5UcoBFoNrFEdKE698Su3OWGNCCAkC7eqWMCuxwncmbphNoKB0XH5Ef7y4tkTKPY7nI+/3ylI7GRof65dKvinHbSzie28b2uSxJykYc3G3x0wMqXtuVz3rg0IZL3swrYW6HMPXmAblK9473CMYKMC2C4lMULP670DpeC56j/9eckK9SmAU1y20VBwJEPEFFq7sktHn1oz+GB3wHC050yWzHXjnR7E9laCEdBT63nXlnYJOwoOzBLKKH2UWGTj7OKDfnkX/Do2PNVC+k0oRgU43cVC9AwMeFZOYXN4r0PIeDvaSxV/y99wjI8Uf7hG2aRNN24/lKjThZxRj+EshTVLSNoxWBjytL2QsqmHpLfnGGtgrkQo65g8xGHRFXDCvVGwLVgP3Z37jl/1HpZoY2rE0uQEmjarOuhzjINlLDteM= apache@imagweb

write_files:
  # Static IP cim az ethernet porton, telepiteshez default gw es DNS is kell
  - content: |
      auto lo
      iface lo inet loopback
    path: /etc/network/interfaces.d/lo
  - content: |
      auto eth0
      iface eth0 inet static
          address *************/24
          dns-nameservers ************* ************ ******* *******
          gateway **************
    path: /etc/network/interfaces.d/eth0
    # Vodafone GSM halozat eleresehez (standardnet = publikus IP cimet kapunk)
  - content: |
      iface wwan0 inet dhcp
          pre-up wvdial wwan-up || true
          post-up /home/<USER>/rendszer/imagmernok-ping.sh
          post-down wvdial wwan-reset || true
          post-down sleep 10
    path: /etc/network/interfaces.d/wwan0
  - content: |
      [Dialer Defaults]
      Modem Type = USB Modem
      ISDN = 0
      Modem = /dev/ttyUSB2
      Baud = 38400

      [Dialer modem-hangup]
      Init1 = ATH

      [Dialer info-gsm]
      Init1 = AT+CSQ
      Init2 = AT+CPIN?
      Init3 = AT+COPS?
      Init4 = AT+CREG?
      Init5 = AT+CPSI?

      [Dialer wwan-up]
      Init1 = AT$QCRMCALL=1,1

      [Dialer wwan-reset]
      Init1 = AT+CRESET
    path: /tmp/wvdial.conf
  - content: |
      blacklist qmi_wwan
    path: /etc/modprobe.d/no-qmi-wwan.conf
  - content: |
      noauth
      name wvdial
      usepeerdns
      nobsdcomp
      novjccomp
      nopcomp
      noaccomp
    path: /etc/ppp/peers/wvdial  # halozati hiba eseten wwan0 ujra vagy vegso esetben ujrainditas
  - content: |
      #*/2 * * * * root /home/<USER>/rendszer/check-eth-status.sh

      */15 * * * * root /home/<USER>/rendszer/check-wwan-status.sh
      3,8,13,18,23,28,33,38,43,48,53,58 * * * * root /home/<USER>/rendszer/check-restart.sh
    path: /etc/cron.d/wwan_check
    owner: root:root
  # SDR vevo biztos jo driverrel menjen
  - content: |
      blacklist dvb_usb_rtl28xxu
      blacklist rtl2832
      blacklist rtl2830
    path: /etc/modprobe.d/no-rtl.conf
  # ertelmesebb rendszer log datumozas tomorites nelkul
  - content: |
      /var/log/syslog {
      	rotate 90
      	daily
      	missingok

      	# notifempty
      	# delaycompress
      	# compress
      	nocompress
      	dateext

      	postrotate
      		/usr/lib/rsyslog/rsyslog-rotate
      	endscript
      }
      /var/log/mail.info
      /var/log/mail.warn
      /var/log/mail.err
      /var/log/mail.log
      /var/log/daemon.log
      /var/log/kern.log
      /var/log/auth.log
      /var/log/user.log
      /var/log/lpr.log
      /var/log/cron.log
      /var/log/debug
      /var/log/messages {
      	rotate 15
      	weekly
      	missingok

      	# notifempty
      	# compress
      	# delaycompress
      	nocompress
      	dateext

      	sharedscripts
      	postrotate
      		/usr/lib/rsyslog/rsyslog-rotate
      	endscript
      }
    path: /etc/logrotate.d/rsyslog
    owner: root:root
  # 4G modem szoftveres bekapcsolas, hogy lehessen aramtalanitani is, ha kell
  - content: |
      [Unit]
      Description=GSM 4G modem power on

      [Service]
      Type=oneshot
      ExecStart=/home/<USER>/rendszer/init-sim7600.sh

      [Install]
      WantedBy=default.target
    path: /etc/systemd/system/init-sim7600.service
  # sajat szolgaltatas(ok) (halozatra mindenkepp varni kell az indulashoz)
  - content: |
      [Unit]
      Description=CESVA TA120 zajmonitoring allomas adatgyujto kliens
      After=ntp.service
      AssertPathExists=/home/<USER>
      # vegtelensegig probaljon ujraindulni
      StartLimitInterval=0

      [Service]
      Type=simple
      User=admin
      WorkingDirectory=/home/<USER>
      ExecStart=/home/<USER>/rendszer/ta120-start.sh
      StandardOutput=inherit
      StandardError=inherit
      PrivateTmp=true
      NoNewPrivileges=true
      # minden subprocess max. 5s alatt leall -> osszesen: 25s
      TimeoutStopSec=30
      RestartSec=60
      Restart=always

      [Install]
      WantedBy=multi-user.target
    path: /etc/systemd/system/ta120-client.service
  # SMS kezeleshez gnokii config (kinai F modem nem tamogatja)
  - content: |
      [global]
      model = AT
      port = /dev/ttyUSB2
      connection = serial
      serial_baudrate = 38400
    path: /home/<USER>/.config/gnokii/config
  # GPIO pinek kezelese root jogosultsag nelkul
  - content: |
      SUBSYSTEM=="bcm2835-gpiomem", KERNEL=="gpiomem", GROUP="kmem", MODE="0660"
      SUBSYSTEM=="gpio", KERNEL=="gpiochip*", ACTION=="add", PROGRAM="/bin/sh -c 'chown root:kmem /sys/class/gpio/export /sys/class/gpio/unexport ; chmod 220 /sys/class/gpio/export /sys/class/gpio/unexport'"
      SUBSYSTEM=="gpio", KERNEL=="gpio*", ACTION=="add", PROGRAM="/bin/sh -c 'chown root:kmem /sys%p/active_low /sys%p/direction /sys%p/edge /sys%p/value ; chmod 660 /sys%p/active_low /sys%p/direction /sys%p/edge /sys%p/value'"
    path: /etc/udev/rules.d/99-gpio.rules
  # RTC oraallitashoz
  - content: |
      # On the Raspberry Pi the RTC isn't available when systemd tries,
      # set the time from RTC now when it is available.
      KERNEL=="rtc0", RUN+="/sbin/hwclock --rtc=$root/$name --hctosys"
    path: /etc/udev/rules.d/85-hwclock.rules

runcmd:
  # ezt ne kelljen DNS-bol keresgetni
  - "echo '************     www.imagmernok.hu  demo.ites-kvmod.hu' >> /etc/hosts"
  # i2c parancssori eszkozok mukodjenek alapbol
  - "echo 'i2c-dev' >> /etc/modules"
  # ---- BUSTER-nel kezzel kell csinalni az osszes frissitest es telepitest
  - "date -s '2022-07-05 13:00'"
  - [apt-get, --yes, update, --allow-releaseinfo-change]
  - [apt-get, --yes, upgrade]
  # install
  - [ apt-get, --yes, install,
  # forrasfajlok kicsomagolasahoz
      unzip,
  # cpu terheltseg vizsgalathoz
      sysstat,
  # SMS kezeleshez
      gnokii-cli,
  # i2c buszos eszkozok tesztjehez
      i2c-tools,
  # GSM es GPS hasznalatahoz (USB-serial)
      python3-serial,
  # python csomagok kesobbi telepitesehez
      python3-pip,
  # OLED display kezeleshez
      python3-pil,
      fonts-freefont-ttf,
  # mozgasdetektalashoz OpenCV-vel
      python3-numpy,
      python3-yaml,
      libatlas-base-dev,
      libatlas3-base,
      libhdf5-dev,
      libhdf5-serial-dev,
      libqtcore4,
      libqtgui4,
      libqt4-test,
      libgstreamer1.0-0,
      libsz2,
      libharfbuzz0b,
      libtiff5,
      libjasper1,
      libilmbase23,
      libopenexr23,
      libavcodec58,
      libavformat58,
      libavutil56,
      libswscale5,
  # ha kell kesobb valamit szerkeszteni
      vim,
  # GSM nethez
      wvdial, minicom,
  # met. allomas jeleinek radios olvasasahoz
      libtool, libusb-1.0.0-dev, librtlsdr-dev, rtl-sdr,
      build-essential, autoconf, cmake, pkg-config ]
  # devsensors halozat ellenorzeshez
  - [ pip3, install, psutil ]
  # homeronek kell kulon fuggvenykonyvtar
  - [ pip3, install, RPi.bme280 ]
  # OLED kijelzonek is kell kulon fuggvenykonyvtar
  - [ pip3, install, Rpi-GPIO, Adafruit-SSD1306, adafruit-circuitpython-ads1x15 ]
  # OpenCV mozgaserzekeleshez
  - [ pip3, install, opencv-python, opencv-contrib-python, imutils ]
  # SEN-55 pormerohoz
  - [ pip3, install, sensirion-i2c-sen5x ]
  # sajat szolgaltatas telepitese, eth0 halozat mindenkeppen kell hozza
  - "unzip /boot/ta120-client.zip -d /home/<USER>/boot/ta120-client.zip"
  - "mkdir /home/<USER>/logs"
  - "chown admin -R /home/<USER>"
  - "chmod +x /home/<USER>/rendszer/ta120-start.sh"
  - "chmod +x /home/<USER>/rendszer/check-wwan-status.sh"
  - "chmod +x /home/<USER>/rendszer/check-eth-status.sh"
  - "chmod +x /home/<USER>/rendszer/check-restart.sh"
  - "chmod +x /home/<USER>/rendszer/imagmernok-ping.sh"
  - "chmod +x /home/<USER>/rendszer/init-sim7600.sh"
  - "chmod +x /home/<USER>/rendszer/minicom.py"
  # gnokii lognak kell
  - "mkdir -p /home/<USER>/.cache/gnokii"
  - "chown admin -R /home/<USER>/.cache"
  - "chown admin -R /home/<USER>/.config"
  # net konfig masolas es sajat szolgaltatas inditasa majd reboot utan
  - [ cp, /tmp/wvdial.conf, /etc/wvdial.conf ]
  - [ systemctl, enable, ta120-client ]
  - [ systemctl, enable, init-sim7600 ]
  # ami nem kell, azt kikapcsoljuk
  - [ systemctl, disable, docker ]
  - [ systemctl, disable, containerd ]
  - [ systemctl, disable, wpa_supplicant ]
  - [ systemctl, disable, wifi-country ]
  - [ systemctl, disable, bluetooth ]
  - [ systemctl, disable, avahi-daemon ]
  - [ rm, /etc/network/interfaces.d/50-cloud-init.cfg ]
  # majd inkabb hardveres orat hasznalunk
  - [ systemctl, disable, fake-hwclock ]
  - [ apt-get, -y, remove, fake-hwclock ]
  # tuzfall install
  - "echo iptables-persistent iptables-persistent/autosave_v4 boolean false | debconf-set-selections"
  - "echo iptables-persistent iptables-persistent/autosave_v6 boolean false | debconf-set-selections"
  - "apt-get -y install iptables-persistent"
  - "mv /home/<USER>/rendszer/firewall.rules /etc/iptables/rules.v4"
  - [ systemctl, enable, netfilter-persistent ]
  # helyi levelek ne vesszenek el
  - "echo dma dma/mailname string $(hostname -f) | debconf-set-selections"
  - "echo dma dma/relayhost string '' | debconf-set-selections"
  - "apt-get -y install dma mutt"
  # watchdog aktivalas
  - "sed -i s/#RuntimeWatchdogSec=0/RuntimeWatchdogSec=10s/ /etc/systemd/system.conf"
  - "sed -i s/#ShutdownWatchdogSec=10min/ShutdownWatchdogSec=10min/ /etc/systemd/system.conf"
  # rtl install:  git clone https://github.com/merbanan/rtl_433
  - "cd /home/<USER>/rendszer/rtl_433/ && mkdir build && cd build && cmake .. && make && make install"
  # wwan driver install:
  - "cd /home/<USER>/rendszer/sim-wwan/ && make install"

# ha minden keszen van, akkor ujrainditunk, hogy minden beallitas betoltodjon
power_state:
  mode: reboot
  timeout: 30

final_message: "boot osszesen: $UPTIME s ->
  1. vezetekes default gw mar nem kell,
  2. apt install console-data a billentyuzet allitashoz,
  3. date && hwclock -w az elso oraallitashoz."
