# camera settings, see http://elinux.org/RPiconfig#Camera
start_x=1
disable_camera_led=1
gpu_mem=128

# Enable serial console
enable_uart=1

# Enable I2C for bme280
dtparam=i2c_arm=on
# Enable RTC
dtoverlay=i2c-rtc,ds3231

# turn wifi and bluetooth off
dtoverlay=pi3-disable-wifi
dtoverlay=pi3-disable-bt

# safe shutdown
dtoverlay=gpio-shutdown,gpio_pin=25

# watchdog is enabled by default in Rpi 3 but to be on the safe side
dtparam=watchdog=on

# Adafruit hdmi display is not detected well
hdmi_force_hotplug=1

# force a specific HDMI mode (here we are forcing 800x480!)
hdmi_group=2
hdmi_mode=87
hdmi_cvt=800 480 60 6 0 0 0
hdmi_drive=1

# more stable USB?
max_usb_current=1
