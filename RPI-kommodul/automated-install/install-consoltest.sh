#!/bin/bash

echo "cloud-config-consoltest.yml ékezet nélküliség ellenőrz<PERSON>"
iconv -f UTF-8 -t ASCII cloud-config-consoltest.yml > jokonfig.txt || echo "jokonfig.txt-t nézd meg!"
read -rsn1 -p"Mehet tovább?"
echo
rm jokonfig.txt

echo "cloud-config-consoletest.yml szintaxis ellenőrzés"
python3 -c 'import yaml, sys; print(yaml.safe_load(sys.stdin))' < cloud-config-consoltest.yml > /dev/null
read -rsn1 -p"Mehet tovább?"
echo

echo "5 legfrissebb elérhető HypriotOS Linux verzió:"
VLIST=$(curl -s https://blog.hypriot.com/downloads/ | grep ".img.zip" | head -5 | cut -d"/" -f8)
echo "${VLIST}"
VERSION=$(echo "${VLIST}" | head -1)
echo "<PERSON><PERSON><PERSON> alapján a legfrissebb verzió: ${VERSION}. ENTER: ez maradjon, vagy írj egy konkrét verziószámot: "
read -r valami
if [ -n "$valami" ]
then
    VERSION=$valami
fi

BASEURL="https://github.com/hypriot/image-builder-rpi/releases/download"
./flash \
    --userdata cloud-config-consoltest.yml \
    --bootconf bootconf.txt \
    --file network-config \
    ${BASEURL}/"${VERSION}"/hypriotos-rpi-"${VERSION}".img.zip
