#cloud-config
# vim: syntax=yaml

# FIGYELEM: EKEZETES KARAKTEREK NEM HASZNALHATOAK A FAJLBAN!!!

# Set your hostname here, the manage_etc_hosts will update the hosts file entries as well
hostname: daspi64-consoltest
manage_etc_hosts: true
# don't write debian.org into apt mirrors
apt:
  preserve_sources_list: true

resize_rootfs: true
growpart:
    mode: auto
    devices: ["/"]
    ignore_growroot_disabled: false

package_update: false
package_upgrade: false
package_reboot_if_required: false
packages:
  # ha kell kesobb valamit szerkeszteni
  - vim

locale: "en_US.UTF-8"
timezone: "Europe/Budapest"

users:
  - name: admin
    gecos: "IMAGMERNOK admin"
    primary-group: users
    shell: /bin/bash
    sudo: ALL=(ALL) NOPASSWD:ALL
    groups: users,docker,adm,dialout,audio,plugdev,netdev,video,i2c,kmem
    ssh-import-id: None
    lock_passwd: false
    # generalas: # openssl passwd -1 -salt 5p2bJZT jelszo
    passwd: $1$5p2bJZT$nNYmhHHfL.v7eUzmQ/cnA.
    ssh-authorized-keys:
      - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIFzmq0VyTcS5l8M8DcCHsBnkwsFq1l8wYpeLCTunaKJU fero@ferolaptop

#write_files:
#  # Static IP cim az ethernet porton, telepiteshez default gw es DNS is kell
#  - content: |
#      persistent
#      # Generate Stable Private IPv6 Addresses instead of hardware based ones
#      slaac private
#      interface eth0
#      static ip_address=*************/24
#      # static ip6_address=fd51:42f8:caae:d92e::ff/64
#      static routers=**************
#      static domain_name_servers=************* ************ ******* *******
#    path: /etc/dhcpcd.conf

runcmd:
  # inkabb USB 1.1 sebesseget allitunk, mert sokkal stabilabb es megbizhatobb
  #- "cat /boot/cmdline.txt | tr -d '\n' > /boot/cmdline1.txt && mv /boot/cmdline1.txt /boot/cmdline.txt"
  #- "echo ' dwc_otg.speed=1' >> /boot/cmdline.txt"
  # i2c parancssori eszkozok mukodjenek alapbol
  - "echo 'i2c-dev' >> /etc/modules"

# ha minden keszen van, akkor ujrainditunk, hogy az USB 1.1 eletbe lepjen,
#   illetve, hogy teszteljuk a normal inditast is
power_state:
  mode: reboot
  timeout: 5

final_message: "boot osszesen: $UPTIME s"
