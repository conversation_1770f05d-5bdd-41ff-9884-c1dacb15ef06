#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Union, Callable, Any, Optional
import functools
import logging
import multiprocessing as mp
import multiprocessing.queues as mpq
import queue
import signal
import time
import multiprocessing


# mpq logoláshoz dekorátor
# ------------------------
def add_mpobj_logger(method) -> Callable:
    @functools.wraps(method)
    def wrapped(obj, *args, **kwargs):
        inst_name = kwargs.get('name', None)
        if not inst_name:
            inst_name = args[0] if len(args) > 0 else 'MAIN'
        obj.logger = logging.getLogger(
            obj.__module__ + '.' + obj.__class__.__name__ + ' :: ' + inst_name
        )
        return method(obj, *args, **kwargs)
    return wrapped


# process várakozás kezeléshez
# ----------------------------
def sleep_secs(max_sleep: float, end_time=999999999999999.9) -> float:
    # Calculate time left to sleep, no less than 0
    return max(0.0, min(end_time - time.time(), max_sleep))


class EventMessage:
    """
    szabványosított üzenetformátum a process-ek közötti kommunikációhoz
    """

    def __init__(self, msg_src: str, msg_type: str, msg: Union[int, str, list, dict]) -> None:
        self.id = time.time()
        self.msg_src = msg_src
        self.msg_type = msg_type
        self.msg = msg

    def __str__(self) -> str:
        return f"{self.msg_src} - {self.msg_type} : {self.msg}"


class MPQueue(mpq.Queue):
    """
    Multiprocess Queue saját névvel és kibővített szogláltatásokkal,
    pl. olyan blokkoló get-tel, ami figyel a leállítási jelzésre
    """

    DEFAULT_POLLING_TIMEOUT = 0.5

    # -- See StackOverflow Article :
    #   https://stackoverflow.com/questions/39496554/cannot-subclass-multiprocessing-queue-in-python-3-5
    #
    # -- tldr; mp.Queue is a _method_ that returns an mpq.Queue object.  That object
    # requires a context for proper operation, so this __init__ does that work as well.
    def __init__(self, name: str, shutdown_event: mp.Event, *args, **kwargs) -> None:
        self.name = name
        self.shutdown_event = shutdown_event
        ctx = mp.get_context()
        super().__init__(*args, **kwargs, ctx=ctx)

    def blocking_get(self) -> EventMessage:
        while True:
            event = self.safe_get(timeout_s=self.DEFAULT_POLLING_TIMEOUT)
            if event:
                break
            if self.shutdown_event.is_set():
                event = EventMessage(f'MPQ:{self.name}', 'SHUTDOWN', 'END')
                break
        return event

    def safe_get(self, timeout_s: Optional[float] = None) -> Optional[EventMessage]:
        try:
            if timeout_s is None:
                return self.get(block=False)
            else:
                return self.get(block=True, timeout=timeout_s)
        except queue.Empty:
            return None

    def safe_put(self, item: EventMessage, timeout: float = DEFAULT_POLLING_TIMEOUT) -> bool:
        try:
            self.put(item, block=False, timeout=timeout)
            return True
        except queue.Full:
            return False

    def drain(self) -> iter:
        item = self.safe_get()
        while item:
            yield item
            item = self.safe_get()


class TerminateInterrupt(BaseException):
    pass


class SignalProxy:
    """
    A SignalProxy továbbítja a leállítási kérelmet jelző op.rendszer-jelzéseket az egyes process-ek felé
    """

    MAX_TERMINATE_CALLED = 3

    def __init__(self, shutdown_event: mp.Event) -> None:
        self.terminate_called = 0
        self.shutdown_event = shutdown_event
        self.watched_signals = [
            (signal.SIGINT, KeyboardInterrupt),
            (signal.SIGTERM, TerminateInterrupt)
        ]

    def init_signals(self) -> None:
        for signal_num, exception_class in self.watched_signals:
            signal.signal(signal_num, self.def_sighandler(exception_class))
            signal.siginterrupt(signal_num, False)

    def def_sighandler(self, exception_class: Callable) -> Callable:
        # noinspection PyUnusedLocal
        def handler(signal_num, current_stack_frame) -> Any:
            self.terminate_called += 1
            self.shutdown_event.set()
            if self.terminate_called >= self.MAX_TERMINATE_CALLED:
                raise exception_class()
        return handler


class StreamingOutput(object):
    """
    kamera képek továbbításához a mozgásérzékelés és a http process között
    """

    def __init__(self):
        self.pipe_out, self.pipe_in = multiprocessing.Pipe(duplex=False)
        self.frame_needed_event = multiprocessing.Event()
        self.frame_needed_event.clear()

    def put(self, newframe):
        self.pipe_in.send(
            cv2.imencode('.jpg', newframe)[1].tobytes()
        )

    def get(self):
        return self.pipe_out.recv()
