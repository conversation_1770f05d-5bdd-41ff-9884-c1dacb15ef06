#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Egy multiprocess architektúra alapvető elemei:
    1. TimerprocWorker: időzített munkaprocess,
    2. QueueProcWorker: egy munkaqueue-b<PERSON><PERSON>ad feladatok és az alapj<PERSON>,
    3. IMonProcWorker: egy adott kö<PERSON>v<PERSON> figyeli a fájlváltozásokat
"""
import multiprocessing as mp
import sys
import time
# import inotify.adapters

from . import base


# ----------------------
# -- munka process-ek --
# ----------------------
class BaseProc:
    """
    Alapfunkciók és közös ős a munkát végz<PERSON> process-ek számára
    """

    @base.add_mpobj_logger
    def __init__(self, name: str, startup_event: mp.Event,
                 shutdown_event: mp.Event, event_q: base.MPQueue, *args) -> None:
        self.name = name
        self.startup_event = startup_event
        self.shutdown_event = shutdown_event
        self.event_q = event_q
        self.terminate_called = 0
        self.init_args(*args)
        base.SignalProxy(self.shutdown_event).init_signals()

    def init_args(self, *args) -> None:
        if args:
            raise ValueError(f"Unexpected arguments to ProcWorker.init_args: {args}")

    def main_loop(self) -> None:
        self.logger.debug("Entering main_loop")
        while not self.shutdown_event.is_set():
            self.main_func()

    def startup(self) -> None:
        self.logger.debug("Entering startup")
        pass

    def shutdown(self) -> None:
        self.logger.debug("Entering shutdown")
        pass

    def main_func(self, *args) -> None:
        self.logger.debug(f"Entering virtual main_func with {args}")
        raise NotImplementedError(f"{self.__class__.__name__}.main_func is not implemented")

    def __call__(self, *args, **kwargs) -> None:
        """
        Callable instance segítségével lesz indítható mp.Process-ként
        """
        try:
            self.startup()
            self.startup_event.set()
            self.main_loop()
        except BaseException as exc:
            # -- Catch ALL exceptions, even Terminate and Keyboard interrupt
            self.logger.error(f"Exception Shutdown: {exc}", exc_info=True)
            self.event_q.safe_put(base.EventMessage(self.name, "FATAL", f"{exc}"))
            if type(exc) in (base.TerminateInterrupt, KeyboardInterrupt):
                sys.exit(1)
            else:
                sys.exit(2)
        finally:
            self.logger.info("Normal Shutdown")
            self.shutdown()

    def wait(self, seconds: int) -> int:
        timetogo = seconds
        while timetogo > 0 and not self.shutdown_event.is_set():
            time.sleep(1)
            timetogo -= 1
        return seconds


# noinspection PyAttributeOutsideInit
class TimerProc(BaseProc):
    """
    Időzített munkaprocess, amely a megadott időközönként éled fel
    """

    MAX_SLEEP_SECS = 0.02

    def init_args(self, interval: float, *args) -> None:
        self.logger.debug(f"Entering TimerProcWorker.init_args : {interval}")
        self.interval_secs = interval
        if args:
            self.mtdtct_pause_event, = args

    def main_loop(self) -> None:
        self.logger.debug("Entering TimerProcWorker.main_loop")
        self.main_func()
        next_time = time.time() + self.interval_secs
        while not self.shutdown_event.is_set():
            time.sleep(
                base.sleep_secs(self.MAX_SLEEP_SECS, next_time)
            )
            if time.time() > next_time:
                self.logger.debug(f"TimerProcWorker.main_loop : calling main_func")
                self.main_func()
                next_time = time.time() + self.interval_secs


# noinspection PyAttributeOutsideInit
class QueueProc(BaseProc):
    """
    Olyan munkaprocess, amely egy megadott queue-t figyel és az abban jövő feladatok alapján végez munkát
    """

    def init_args(self, wq: base.MPQueue) -> None:
        self.work_q = wq
        self.logger.debug(f"Entering QueueProcWorker.init_args  q-name: {self.work_q.name}")

    def main_loop(self) -> None:
        self.logger.debug("Entering QueueProcWorker.main_loop")
        while not self.shutdown_event.is_set():
            item = self.work_q.blocking_get()
            self.logger.debug(f"QueueProcWorker.main_loop received '{item}' message")
            if not item.msg_type == 'SHUTDOWN':
                self.main_func(item)


# noinspection PyAttributeOutsideInit
# class IMonProc(BaseProc):
#     """
#     Különleges munkaprocess, amely egy adott könyvtárban történt fájlváltozásokat figyeli
#     """
#
#     def init_args(self, watchdir: str) -> None:
#         self.logger.debug(f"Entering IMonProcWorker.init_args : {watchdir}")
#         self._watchdir = watchdir
#         self.imon = inotify.adapters.Inotify()
#         self.imon.add_watch(self._watchdir)
#
#     def main_loop(self) -> None:
#         self.logger.debug("Entering IMonProcWorker.main_loop")
#         while not self.shutdown_event.is_set():
#             # -- a timeout biztosítja, hogy a shutdown vizsgálat rendszeresen megtörténhessen
#             for file_event in self.imon.event_gen(timeout_s=3, yield_nones=False):
#                 (_, type_names, path, filename) = file_event
#                 # csak fájlműveletek érdekelnek bennünket, directory listing stb. meg nem
#                 if filename:
#                     self.main_func(filename, type_names)
