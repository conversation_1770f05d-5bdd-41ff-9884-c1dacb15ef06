#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Egy multiprocess architektúra alapvető elemei:
    a MainContext a fő s<PERSON>l, ami irányítja az összes többi munkaprocess-t
"""

import multiprocessing as mp
import time
from typing import Type
from http.server import HTTPServer
from socketserver import ThreadingMixIn
import socket

from . import base
from . import workers


class Proc:
    """
    A fő vezérprocess részére biztosít egy API-t a munka process-ek kezeléséhez
    """

    STARTUP_WAIT_SECS = 10.0
    SHUTDOWN_WAIT_SECS = 10.0

    @base.add_mpobj_logger
    def __init__(self, name: str, worker_class: Type[workers.BaseProc],
                 shutdown_event: mp.Event, event_q: base.MPQueue, *args):
        self.name = name
        self.shutdown_event = shutdown_event
        self.startup_event = mp.Event()
        worker_instance = worker_class(name, self.startup_event, shutdown_event, event_q, *args)
        self.proc = mp.Process(target=worker_instance)
        self.logger.debug(f"Proc.__init__ starting")
        self.proc.start()
        started = self.startup_event.wait(timeout=Proc.STARTUP_WAIT_SECS)
        self.logger.debug(f"Proc.__init__ starting : got {started}")
        if not started:
            self.terminate()
            raise RuntimeError(f"Process {name} failed to startup after {Proc.STARTUP_WAIT_SECS} seconds")

    def full_stop(self, wait_time=SHUTDOWN_WAIT_SECS) -> None:
        self.logger.debug(f"Proc.full_stop stoping")
        self.shutdown_event.set()
        self.proc.join(wait_time)
        if self.proc.is_alive():
            self.terminate()

    def terminate(self) -> bool:
        self.logger.debug(f"Proc.terminate terminating")
        NUM_TRIES = 3
        tries = NUM_TRIES
        while tries and self.proc.is_alive():
            self.proc.terminate()
            time.sleep(0.01)
            tries -= 1

        if self.proc.is_alive():
            self.logger.error(f"Proc.terminate failed to terminate {self.name} after {NUM_TRIES} attempts")
            return False
        else:
            self.logger.info(f"Proc.terminate terminated {self.name} after {NUM_TRIES - tries} attempt(s)")
            return True

    def __enter__(self) -> __qualname__:
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> bool:
        self.full_stop()
        return not exc_type


class MainContext:
    """
    A főprogram vezérprocess-e, amely az egyes process-ek közötti kommunikációt intézi és karmesterkedik
    """

    STOP_WAIT_SECS = 5

    @base.add_mpobj_logger
    def __init__(self, name: str, smsdevice) -> None:
        self.name = name
        self._smsdevice = smsdevice
        self.procs = []
        self.queues = []
        self.shutdown_event = mp.Event()
        self.event_queue = self.MPQueue('main')
        base.SignalProxy(self.shutdown_event).init_signals()
        self.logger.info(f'{self.name} - START')
        self._smsdevice.send(f'{self.name} - START')

    def __enter__(self) -> __qualname__:
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> bool:
        if exc_type:
            self.logger.exception(f"Exception: {exc_val}")
        self._failed_procs = self.stop_procs()
        self.logger.info(f'Hibás process-ek = {self._failed_procs}')
        self._thrown_items = self.stop_queues()
        self.logger.info(f'Q-ból kidobott elemek = {self._thrown_items}')
        self.logger.info(f'{self.name} - STOP')
        if self._failed_procs:
            self._smsdevice.send(f'{self.name} hibás process-ek: {self._failed_procs} - STOP')
        else:
            self._smsdevice.send(f'{self.name} - STOP')
        # -- Don't eat exceptions that reach here.
        return not exc_type

    def Proc(self, name: str, worker_class: Type[workers.BaseProc], *args) -> None:
        self.procs.append(
            Proc(name, worker_class, self.shutdown_event, self.event_queue, *args)
        )

    def TimerProc(self, name: str, worker: Type[workers.TimerProc], interval_sec: int) -> None:
        self.Proc(name, worker, interval_sec)

    def SignallingTimerProc(self, name: str, worker: Type[workers.TimerProc],
                            interval_sec: int, pause_event: mp.Event) -> None:
        self.Proc(name, worker, interval_sec, pause_event)

    def QueueProc(self, name: str, worker: Type[workers.QueueProc], wevent_q: base.MPQueue) -> None:
        self.Proc(name, worker, wevent_q)

    # def IMonProc(self, name: str, worker: Type[workers.IMonProc], watchdir: str) -> None:
    #     self.Proc(name, worker, watchdir)

    def MPQueue(self, name: str, *args, **kwargs) -> base.MPQueue:
        q = base.MPQueue(name, self.shutdown_event, *args, **kwargs)
        self.queues.append(q)
        return q

    def stop_procs(self) -> list:
        self.shutdown_event.set()
        end_time = time.time() + self.STOP_WAIT_SECS
        failed = []
        # -- Wait up to STOP_WAIT_SECS for all processes to complete
        for proc in self.procs:
            proc.proc.join(
                base.sleep_secs(self.STOP_WAIT_SECS, end_time)
            )
        # -- Clear the procs list and _terminate_ any procs that have not yet exited
        still_running = []
        while self.procs:
            proc = self.procs.pop()
            if proc.proc.is_alive():
                if not proc.terminate():
                    still_running.append(proc)
            else:
                exitcode = proc.proc.exitcode
                if exitcode:
                    self.logger.error(f"Process {proc.name} ended with exitcode {exitcode}")
                    failed.append(proc.name)
                else:
                    self.logger.info(f"Process {proc.name} stopped successfully")
        self.procs = still_running
        return failed

    def stop_queues(self) -> int:
        num_items_left = 0
        # -- Clear the queues list and close all associated queues
        for q in self.queues:
            # -- biztonság kedvéért loglunk is
            for item in q.drain():
                num_items_left += 1
                self.logger.info(f'[{q.name}] q-bol kidobva: {item}')
            q.close()
        # -- Wait for all queue threads to stop
        while self.queues:
            q = self.queues.pop(0)
            q.join_thread()
        return num_items_left

    def wait(self, seconds: int) -> None:
        while seconds > 0 and not self.shutdown_event.is_set():
            time.sleep(1)
            seconds -= 1


# egyszerűen leállítható HTTP szerver
class StoppableHTTPServer(ThreadingMixIn, HTTPServer):

    allow_reuse_address = True      # újra lehessen indítani, még ha a socket foglaltat is jelez
    daemon_threads = False          # a szerver várja meg, hogy végezzenek a szálak

    def server_bind(self):
        HTTPServer.server_bind(self)
        self.socket.settimeout(5)

    def get_request(self):
        try:
            sock, addr = self.socket.accept()
            sock.settimeout(None)
            return sock, addr
        except socket.timeout:
            pass
