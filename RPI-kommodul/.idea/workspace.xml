<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="874a54bf-998d-4beb-9099-1d644c91938c" name="Default Changelist" comment="fesz. olvasás hibakezelés + SMS indításnál és leállásnál">
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/automated-install/cloud-config.yml" beforeDir="false" afterPath="$PROJECT_DIR$/automated-install/cloud-config.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/automated-install/flash" beforeDir="false" afterPath="$PROJECT_DIR$/automated-install/flash" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/automated-install/install.sh" beforeDir="false" afterPath="$PROJECT_DIR$/automated-install/install.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/Sensirion_STEP_Mechanical_Design-In_Example_SEN5x.step" beforeDir="false" afterPath="$PROJECT_DIR$/docs/Sensirion_STEP_Mechanical_Design-In_Example_SEN5x.step" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../RPI-met-DAVIS-weewx/skin.conf" beforeDir="false" afterPath="$PROJECT_DIR$/../RPI-met-DAVIS-weewx/skin.conf" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/w_met.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/common/sensors.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/common/datacls.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/common/devices.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/config.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/daspidata.sql" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/common/sensirion.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/w_send.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/recv-noisedata-mpq.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/w_air.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="1ui92zRaWC2xWv287rLfBjKn3fE" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;git-widget-placeholder&quot;: &quot;master&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/common" />
      <recent name="$PROJECT_DIR$/common/funcs.py" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="874a54bf-998d-4beb-9099-1d644c91938c" name="Default Changelist" comment="" />
      <created>1625137339794</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1625137339794</updated>
    </task>
    <task id="LOCAL-00001" summary="met. adatküldés működik">
      <created>1627158532912</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1627158532912</updated>
    </task>
    <task id="LOCAL-00002" summary="met. process timeout javítás">
      <created>1627243354707</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1627243354707</updated>
    </task>
    <task id="LOCAL-00003" summary="met. process hibakezelés: USB reset vagy akár újraindulás">
      <created>1629729202584</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1629729202584</updated>
    </task>
    <task id="LOCAL-00004" summary="- met. process hibakezelés javítás&#10;- USB azonosító kikeresés név szerint">
      <created>1630488241465</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1630488241465</updated>
    </task>
    <task id="LOCAL-00005" summary="powersource detektálás">
      <created>1631551006843</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1631551006843</updated>
    </task>
    <task id="LOCAL-00006" summary="utolsó 5 fesz. érték javítás">
      <created>1631609046232</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1631609046232</updated>
    </task>
    <task id="LOCAL-00007" summary="met. reset javítás">
      <created>1632045112457</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1632045112457</updated>
    </task>
    <task id="LOCAL-00008" summary="eső javítás">
      <created>1632733702284</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1632733702285</updated>
    </task>
    <task id="LOCAL-00009" summary="hibakezelési javítások">
      <created>1633110320433</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1633110320433</updated>
    </task>
    <task id="LOCAL-00010" summary="átállás sim7600 modem-re">
      <created>1657026711252</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1657026711252</updated>
    </task>
    <task id="LOCAL-00011" summary="met. USB dongle stabilizálás">
      <created>1657532177111</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1657532177111</updated>
    </task>
    <task id="LOCAL-00012" summary="common package-be modul átszervezés">
      <created>1657559055421</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1657559055421</updated>
    </task>
    <task id="LOCAL-00013" summary="crontab interface figyelés javítások">
      <created>1657639816475</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1657639816475</updated>
    </task>
    <task id="LOCAL-00014" summary="SPS30 pormérő integrálás a rendszerbe">
      <created>1658507211878</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1658507211878</updated>
    </task>
    <task id="LOCAL-00015" summary="csere SEN55 pormérőre + hangfelvétel USB mikrofonnal">
      <created>1676398056024</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1676398056024</updated>
    </task>
    <task id="LOCAL-00016" summary="process indítás bedugott kütyük szerint">
      <created>1676494071548</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1676494071548</updated>
    </task>
    <task id="LOCAL-00017" summary="fesz. olvasás hibakezelés + SMS indításnál és leállásnál">
      <created>1681827041299</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1681827041299</updated>
    </task>
    <option name="localTasksCounter" value="18" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="met. adatküldés működik" />
    <MESSAGE value="met. process timeout javítás" />
    <MESSAGE value="met. process hibakezelés: USB reset vagy akár újraindulás" />
    <MESSAGE value="- met. process hibakezelés javítás&#10;- USB azonosító kikeresés név szerint" />
    <MESSAGE value="powersource detektálás" />
    <MESSAGE value="utolsó 5 fesz. érték javítás" />
    <MESSAGE value="met. reset javítás" />
    <MESSAGE value="eső javítás" />
    <MESSAGE value="hibakezelési javítások" />
    <MESSAGE value="átállás sim7600 modem-re" />
    <MESSAGE value="met. USB dongle stabilizálás" />
    <MESSAGE value="common package-be modul átszervezés" />
    <MESSAGE value="crontab interface figyelés javítások" />
    <MESSAGE value="SPS30 pormérő integrálás a rendszerbe" />
    <MESSAGE value="csere SEN55 pormérőre + hangfelvétel USB mikrofonnal" />
    <MESSAGE value="process indítás bedugott kütyük szerint" />
    <MESSAGE value="fesz. olvasás hibakezelés + SMS indításnál és leállásnál" />
    <option name="LAST_COMMIT_MESSAGE" value="fesz. olvasás hibakezelés + SMS indításnál és leállásnál" />
  </component>
</project>