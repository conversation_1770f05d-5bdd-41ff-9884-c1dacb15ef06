<template>
    <div>
        <div class="row gutters row-gutters">
            <div class="col col-6 crd">
                <h4><q-icon name="air" /> <PERSON><PERSON><PERSON>sszes szállópor</h4>
                <div class="crd-content">
                    <span class="weatherdata">PM<sub>10</sub> = {{ $filters.round(actdata.PM10, 1) }} &micro;g/m<sup>3</sup></span>
                    <table class="meta">
                        <tbody>
                            <tr>
                                <td>Max.:</td>
                                <td>{{ $filters.round(actdata.PM10_max, 1) }} &micro;g/m<sup>3</sup></td>
                                <td>({{ actdata.PM10_max_ido }})</td>
                            </tr>
                            <tr>
                                <td>Min.:</td>
                                <td>{{ $filters.round(actdata.PM10_min, 1) }} &micro;g/m<sup>3</sup></td>
                                <td>({{ actdata.PM10_min_ido }})</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col col-6 crd">
                <h4><q-icon name="directions_car" /> Közlekedési PM<sub>2.5</sub> arány</h4>
                <div class="crd-content">
                    <span class="weatherdata"> {{ $filters.round(actdata.forras_arany, 1) }} %</span>
                    <table class="meta">
                        <tbody>
                            <tr>
                                <td>Max.:</td>
                                <td>{{ $filters.round(actdata.fa_max, 1) }} %</td>
                                <td>({{ actdata.fa_max_ido }})</td>
                            </tr>
                            <tr>
                                <td>Min.:</td>
                                <td>{{ $filters.round(actdata.fa_min, 1) }} %</td>
                                <td>({{ actdata.fa_min_ido }})</td>
                            </tr>
                            <!-- <tr>
                                <td colspan="2" class="text-grey-9">
                                    Koncentráció térkép számítás:
                                </td>
                                <td class="text-grey-8">
                                    <q-icon :name="calcstat.icon" :color="calcstat.color" size="sm" />
                                </td>
                            </tr> -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    props: {
        actdata: {
            type: Object,
        },
    },
}
</script>
