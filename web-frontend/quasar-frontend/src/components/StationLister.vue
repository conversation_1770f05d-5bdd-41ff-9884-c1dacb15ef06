<template>
    <q-list bordered class="rounded-borders">
        <!-- eslint-disable-next-line vue/no-v-for-template-key -->
        <template v-for="(meroall, index) in meroList" :key="meroall.eazon">
            <q-item dense>
                <q-icon
                    :name="meroall.icon"
                    size="80px"
                    :class="['transit', meroall.tipus.toLowerCase()]"
                />
                <q-item-section avatar style="height: 140px;">
                    <span :class="['svgicon', 'icon-'+meroall.tipus.toLowerCase()]"></span>
                </q-item-section>
                <q-item-section class="col-8 q-mr-md q-py-sm">
                    <q-item-label>
                        <q-card bordered class="bg-grey-1 q-pa-sm">
                            <div class="row items-center">
                                <q-icon
                                    class="col-1 q-mx-xs" name="assessment" size="lg" color="grey-6"
                                />
                                <div
                                    class="col text-grey-8 text-caption text-center
                                            self-stretch bg-white shadow-up-1
                                            show-act-data"
                                    @mousemove="moveTooltip"
                                >
                                    <template v-if="actData[meroall.eazon]">
                                        {{ dateIdopont(meroall.eazon) }} <br />
                                        {{ timeIdopont(meroall.eazon) }}
                                        <component
                                            :is="'act-'.concat(meroall.tipus.toLowerCase())"
                                            :class="['tooltip', meroall.tipus.toLowerCase()]"
                                            :actdata="actData[meroall.eazon]"
                                        >
                                        </component>
                                    </template>
                                </div>
                                <div class="col-6 text-grey-10 text-weight-bold text-center">
                                    {{ meroall.nev }}
                                </div>
                                <template v-if="actData[meroall.eazon]">
                                    <q-icon
                                        v-if="actData[meroall.eazon].aramforras == 'off'"
                                        class="col-1 q-mx-xs"
                                        name="power_off"
                                        size="sm"
                                        color="indigo-2"
                                    >
                                        <q-tooltip>Üzemen kívül</q-tooltip>
                                    </q-icon>
                                    <q-icon
                                        v-else-if="actData[meroall.eazon].aramforras == 'NA'"
                                        class="col-1 q-mx-xs"
                                        name="mode_standby"
                                        size="xs"
                                        color="light-blue-5"
                                    >
                                        <q-tooltip>Nincs önálló áramellátás</q-tooltip>
                                    </q-icon>
                                    <q-icon
                                        v-else
                                        class="col-1 q-mx-xs"
                                        :name="actData[meroall.eazon].aramforras == '220V' ? 'power' : 'battery_alert'"
                                        size="sm"
                                        color="light-blue-5"
                                    >
                                        <q-tooltip>áramellátás:
                                            {{ actData[meroall.eazon].aramforras == '220V'
                                                ? '220V hálózat'
                                                : 'napenergia 12V akku' }}
                                        </q-tooltip>
                                    </q-icon>
                                </template>
                            </div>
                        </q-card>
                    </q-item-label>
                    <q-item-label caption class="q-pt-sm text-blue-grey-10">
                        {{ chemNameSub(meroall.leiras) }}
                    </q-item-label>
                    <q-item-label class="q-pt-sm text-black">
                        Telepítési helyszín: {{ meroall.hely }}
                    </q-item-label>
                </q-item-section>
                <q-item-section class="q-py-sm">
                    <q-btn
                        v-if="meroall.tipus!='MET'"
                        :to="{ name: 'map',  params: { stationID: meroall.eazon } }"
                        color="primary" icon="map" label="térkép" class="text-medium full-width q-mt-sm q-py-sm"
                    >
                        <q-tooltip
                            class="bg-white text-indigo-10 text-caption shadow-2"
                            anchor="center left" self="center right" :offset="[10, 10]"
                            transition-show="scale" transition-hide="scale"
                        >
                            <span v-if="meroall.tipus=='LEV'">
                                Légzennyezettség koncentrációk megjelenítése digitális térképen
                            </span>
                            <span v-else>
                                Zajszintek ábrázolása digitális térképen
                            </span>
                        </q-tooltip>
                    </q-btn>
                    <q-btn
                        :to="{ name: 'diagram', params: { stationID: meroall.eazon } }"
                        color="primary" icon="assessment" label="diagram" class="text-medium full-width q-my-md q-py-sm"
                    >
                        <q-tooltip
                            class="bg-white text-indigo-10 text-caption shadow-2"
                            anchor="bottom left" self="center right" :offset="[10, 10]"
                            transition-show="scale" transition-hide="scale"
                        >
                            <span v-if="meroall.tipus=='MET'">Diagramok és trendek</span>
                            <span v-else-if="meroall.tipus=='ZAJ'">Idősoros zajszintek és diagramok</span>
                            <span v-else>Idősoros légszennyező-anyag kocentrációk és diagramok</span>
                        </q-tooltip>
                    </q-btn>
                </q-item-section>
            </q-item>
            <q-separator v-if="index != meroList.length - 1" spaced/>
        </template>
    </q-list>
</template>

<script>

import formatMix from '../router/format-mixin'
import pubMixin from './pubMixin'
import ActMet from './Act-Met'
import ActZaj from './Act-Zaj'
import ActLev from './Act-Lev'

export default {
    props: ['userLoc'],

    mixins: [formatMix, pubMixin],

    components: {
        'act-met': ActMet,
        'act-zaj': ActZaj,
        'act-lev': ActLev,
    },

    data () {
        return {
            meroList: [],
            actData: {},
            intervalID: null,
        }
    },

    watch: {
        userLoc: {
            // eslint-disable-next-line no-unused-vars
            handler: function (newValue, oldValue) {
                this.findNearest(newValue)
            },
            deep: true, // az object címe u.a. marad, csak a belső mezők változnak, ezért kell deep
        }
    },

    methods: {
        async getStations () {
            const data = await this.$pubGet('/meroallomasok')
            if (!data) {
                return
            }
            this.meroList = data.map(item => {
                item.icon = ''
                return item
            }) //.sort((a, b) => {
            //return a.id - b.id  // a biztonság kedvéért sorbarendezünk megint
            //})
            this.findNearest(this.userLoc)
        },

        async getActData () {
            this.actData = await this.$pubGet('/actdata')
        },

        moveTooltip (e) {
            let tooltip = e.target.classList.contains('tooltip') ? e.target : e.target.querySelector(':scope .tooltip')
            if (tooltip) {
                tooltip.style.left = e.pageX - tooltip.clientWidth / 2 + 'px'
                tooltip.style.top = e.pageY + 10 + 'px'
                if (e.pageY + 10 + tooltip.clientHeight > window.innerHeight) {
                    tooltip.style.top = e.pageY - 10 - tooltip.clientHeight + 'px'
                }
            }
        },

        findNearest (user) {
            if (!(user.Lat && user.Lng) || Object.keys(this.meroList).length === 0) {
                return
            }
            // legközelebbi állomások jelölése jobra nyíllal
            let minIndex = 0
            let minDist = Infinity
            this.meroList.forEach((meroall, mindex) => {
                meroall.dist = Math.sqrt(
                    Math.pow((meroall.lat - user.Lat), 2) +
                        Math.pow((meroall.lng - user.Lng), 2)
                )
                if (meroall.dist < minDist) {
                    minDist = meroall.dist
                    minIndex = mindex
                }
                meroall.icon = ''
            })
            this.meroList[minIndex].icon = 'arrow_right_alt'
        },

        dateIdopont (muszer) {
            if ('idopont' in this.actData[muszer] === false) {
                return '...'
            }
            return this.actData[muszer].idopont.split(' ')[0]
        },

        timeIdopont (muszer) {
            if ('idopont' in this.actData[muszer] === false) {
                return '...'
            }
            return this.actData[muszer].idopont.split(' ')[1]
        },
    },

    created () {
        this.getStations()
    },

    mounted () {
        this.getActData()
        // 5 percenként van újabb adat, ezért 1 percenként mindig frissítünk
        this.intervalID = window.setInterval(this.getActData, 1 * 60 * 1000)
    },

    beforeUnmount () {
        window.clearInterval(this.intervalID)
    },
}
</script>

<style scoped lang="scss">

$met: rgb(228, 180, 117);
$zaj: rgb(167, 51, 109);
$lev: rgb(46, 130, 199);

.transit {
    position: relative;
    left: -50px;
    top: 35px;
    width: 10px;
}

.met {
    color: $met;
}

.zaj {
    color: $zaj;
}

.lev {
    color: $lev;
}

.svgicon {
    width: 60px;
    height: 60px;
    mask-size: cover;
}

.icon-lev {
    mask: url('~assets/mst-carexhaust.svg');
    background-color: $lev;
}

.icon-met {
    mask: url('~assets/mst-met.svg');
    background-color: $met;

}

.icon-zaj {
    mask: url('~assets/mst-noiselevel.svg');
    background-color: $zaj;
}

.show-act-data {
    cursor: pointer;
}

.show-act-data:hover .tooltip {
    display: block;
}

.tooltip {
    position: fixed;
    white-space: nowrap;
    display: none;
    background: #fffaf4;
    border: 1px solid #ccc;
    padding: 5px;
    z-index: 1000;
}

.tooltip .met {
    width: 1000px;
}

.tooltip .zaj {
    width: 800px;
}

.tooltip .lev {
    width: 100px;
}
</style>
