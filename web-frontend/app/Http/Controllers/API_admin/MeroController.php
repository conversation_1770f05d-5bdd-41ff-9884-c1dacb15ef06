<?php

namespace App\Http\Controllers\API_admin;

use App\Http\Controllers\Controller;
use App\Muszer;

use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class MeroController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        return $this->respondOK(
            Muszer::where('aktiv', true)
                ->get()
                ->sortByDesc(fn($muszer) => $muszer->utmer_idopont)
                ->values()
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'eazon' => 'required|unique:muszerek',
            'nev' => 'required|string',
            'tipus' => 'required|alpha',
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
            'eovx' => 'required|numeric',
            'eovy' => 'required|numeric',
            'leiras' => 'required|string',
            'hely' => 'required|string',
            'sorozatszam' => 'required|string',
            'magassag' => 'nullable|numeric',
            'meretek' => 'nullable|string',
            'fenykep' => 'required|file',
            'IP_port' => 'nullable|numeric',
            'SIM_tszam' => 'required|string',
            'SIM_elofiz_e' => 'required|boolean',
            'SIM_erv_datig' => 'nullable|date',
            'SIM_havidij' => 'nullable|numeric',
            'gyarto' => 'required|string',
            'gyarto_kontakt' => 'required|string',
            'gyarto_tel' => 'required|string',
            'gyarto_spec' => 'required|json',
        ]);
        $muszer = new Muszer;
        $muszer->fill($validatedData);
        // kiterjesztés nélkül mentjük, úgyis a fejléc alapján állítja be a fájltípust a webszerver
        $request->fenykep->storeAs('public', $muszer->eazon);
        $muszer->fenykep = $request->fenykep->getClientOriginalName();
        $muszer->save();
        return $this->respondCreated([
            'status' => 'OK',
            'id' => $muszer->id,
        ]);
    }

    /**
     * Publikus webfelület számára limitált információk
     *
     * @param  string  $muszeazon
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($muszeazon)
    {
        $muszer = Muszer::where('eazon', $muszeazon)->firstOrFail([
            'eazon', 'nev', 'tipus', 'lat', 'lng', 'leiras', 'hely', 'id', 'sorozatszam'
        ]);
        return $this->respondOK($muszer);
    }

    /**
     * Adminnak teljes infócsomag
     *
     * @param  string  $muszeazon
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminshow($muszeazon)
    {
        $muszer = Muszer::where('eazon', $muszeazon)
            ->firstOrFail()
            ->append('allapot')
            ->append('ipcimek');
        return $this->respondOK($muszer);
    }

    /**
     * Adminnak műszeradatok diagramos ábrázoláshoz
     *
     * @param  string  $muszeazon
     * @param  string  $adatsornev
     * @param  int  $idotartam
     * @return \Illuminate\Http\JsonResponse
     */
    public function adatsor($muszeazon, $adatsornev, int $idotartam)
    {
        $muszer = Muszer::where('eazon', $muszeazon)->firstOrFail();

        switch ($muszer->tipus) {
            case 'ZAJ':
                $adatmezo = 'doboz_adat';
                $mapfunc = function ($item) use ($adatsornev) {
                    return [
                        $item->idopont,
                        round(Arr::get($item->doboz_adat, $adatsornev, 0), 1)
                    ];
                };
            break;
            case 'MET':
                $adatmezo = $adatsornev;
                $mapfunc = function ($item) use ($adatmezo) {
                    return [
                        $item->idopont->format('Y-m-d H:i'),
                        round($item->$adatmezo, 1)
                    ];
                };
            break;
            case 'LEV':
            default:
                $adatmezo = 'NOx';
                $mapfunc = function ($item) use ($adatmezo) {
                    return [
                        $item->idopont->format('Y-m-d H:i'),
                        round($item->$adatmezo, 1)
                    ];
                };
            break;
        }

        $adatsor = $muszer->meresek()
                ->where('idopont', '>', CarbonImmutable::now()->subDays($idotartam))
                ->whereNotNull($adatmezo)
                ->orderBy('idopont')
                ->get(['idopont', $adatmezo])
                ->map($mapfunc)
                ->all();
        return $this->respondOK(
             count($adatsor) == 0
                ? ['empty' => true, 'message' => "nincs adat az elmúlt $idotartam napból"]
                : $adatsor
        );
    }
}
