<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Exception;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;


class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * @param string|array|\Exception $message
     * @param  string|null  $key
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function respondNotFound($message, ?string $key = 'error'): JsonResponse
    {
        $message = $message instanceof Exception
            ? $message->getMessage()
            : $message;

        return $this->apiResponse(
            [$key => $message],
            Response::HTTP_NOT_FOUND
        );
    }

    /**
     * @param array|\illuminate\Support\Collection|Model/array $contents
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function respondOK($contents = []): JsonResponse
    {
        $data = [] === $contents
            ? ['success' => true]
            : $contents;

        return $this->apiResponse($data);
    }

    public function respondUnAuthenticated(?string $message = null): JsonResponse
    {
        return $this->apiResponse(
            ['error' => $message ?? 'Unauthenticated'],
            Response::HTTP_UNAUTHORIZED
        );
    }

    public function respondForbidden(?string $message = null): JsonResponse
    {
        return $this->apiResponse(
            ['error' => $message ?? 'Forbidden'],
            Response::HTTP_FORBIDDEN
        );
    }

    public function respondError(?string $message = null): JsonResponse
    {
        return $this->apiResponse(
            ['error' => $message ?? 'Error'],
            Response::HTTP_BAD_REQUEST
        );
    }

    public function respondCreated($data = []): JsonResponse
    {
        return $this->apiResponse($data, Response::HTTP_CREATED);
    }

    /**
     * @param string|\Exception $message
     * @param  string|null  $key
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function respondFailedValidation($message, ?string $key = 'message'): JsonResponse
    {
        $message = $message instanceof Exception
            ? $message->getMessage()
            : $message;

        return $this->apiResponse(
            [$key => $message ?? 'Validation errors'],
            Response::HTTP_UNPROCESSABLE_ENTITY
        );
    }

    public function respondNoContent($data = []): JsonResponse
    {
        return $this->apiResponse($data, Response::HTTP_NO_CONTENT);
    }

    private function apiResponse($data, int $code = 200): JsonResponse
    {
        // bármi lehet a $data, mert átalakításra kerül array-re, mielőtt json lesz belőle
        return response()->json($data, $code);
    }
}
