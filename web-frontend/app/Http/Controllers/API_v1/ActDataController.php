<?php

namespace App\Http\Controllers\API_v1;

use App\Muszer;
use App\ZajMeres;
use App\MetMeres;

use App\Http\Controllers\Controller;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class ActDataController extends Controller
{
    /**
     * Minden műszerből az aktuális adatok kigyűjtése
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke()
    {
        // DB::enableQueryLog();
        return $this->respondOK(
            Muszer::where('aktiv', true)->get(['id', 'tipus', 'eazon'])->mapWithKeys(function ($m) {

                // ha még nem működik valamelyik műszer, akkor üres info ablak lesz az eredmény
                $result = ['aramforras' => 'off'];

                if ($m->tipus == 'MET') {
                    $mertAdat = [
                        'idopont',
                        'legnyomas',
                        'harmatpont',
                        'besugarzas',
                        'szelseb',
                        'szelirany',
                        'szelirany_kat',
                        'homerseklet',
                        'relpara',
                        'eso',
                        'felhoalap',
                    ];
                    $statAdat = [
                        'hom' => 'homerseklet',
                        'para' => 'relpara',
                        'lgny' => 'legnyomas',
                        'hp' => 'harmatpont',
                        'szs' => 'szelseb',
                    ];
                    $merestipus = 'App\MetMeres';

                } elseif ($m->tipus == 'ZAJ') {
                    $mertAdat = [
                        'idopont',
                        'LAeq5m_mert',
                        'LAeq5m_szam',
                        'zajterheles_arany',
                        'szam_status',
                        'doboz_adat',
                    ];
                    $statAdat = [
                        'Lm' => 'LAeq5m_mert',
                        'Lsz' => 'LAeq5m_szam',
                        'zta' => 'zajterheles_arany',
                    ];
                    $merestipus = 'App\ZajMeres';

                } elseif ($m->tipus == 'LEV') {
                    $mertAdat = [
                        'idopont',
                        'NOx',
                        'PM10',
                        'forras_arany',
                    ];
                    $statAdat = [
                        'NOx' => 'NOx',
                        'PM10' => 'PM10',
                        'fa' => 'forras_arany',
                    ];
                    $merestipus = 'App\LevMeres';
                }

                $meres = $m->legutolsomeres($mertAdat);
                if ($meres) {
                    // Log::info(print_r($meres, true));
                    $tegnap_ilyenkortol = $meres->idopont->subHours(24);
                    $result = array_merge(
                        $result,
                        $meres->toArray()
                    );
                    foreach ($statAdat as $rovidites => $mezonev){
                        foreach (['max', 'min'] as $stattipus) {
                            $stat = $merestipus::stat($tegnap_ilyenkortol, $mezonev, $stattipus);
                            $result = array_merge($result, [
                                $rovidites.'_'.$stattipus => $stat->$mezonev,
                                $rovidites.'_'.$stattipus.'_ido' => $stat->idopont->format('H:i'),
                                // $rovidites.'_'.$stattipus.'_memuse' => memory_get_usage(),
                            ]);
                        }
                    }
                    // $result = array_merge($result, ['qlog' => DB::getQueryLog()]);

                    // alapértelmezetten nincs önálló áramforrása a műszernek
                    $result['aramforras'] = 'NA';

                        // meteorológiánál még külön hozzárakunk spec. adatokat
                    if ($m->tipus == 'MET') {
                        $egyoravalkorabban = $meres->idopont->subHours(1);
                        $result['idopont'] = $meres->idopont->toDateTimeString();
                        $result['szelirany'] = intval($result['szelirany']);
                        $result['szelir_atlag'] = intval(
                            MetMeres::select(['szelirany'])
                                ->where('idopont', '>', $egyoravalkorabban)
                                ->avg('szelirany')
                        );
                        $result['szs_atlag'] = MetMeres::select(['szelseb'])
                            ->where('idopont', '>', $egyoravalkorabban)
                            ->avg('szelseb');
                        # https://en.wikipedia.org/wiki/Wind_chill
                        $result['hom_erzet'] = 13.12 + 0.6215 * $result['homerseklet'] -
                            11.37 * ($result['szelseb']*3.6)**0.16 + 0.3965 * $result['homerseklet'] * ($result['szelseb']*3.6)**0.16;

                    // zajnál (központi adattovábbító) pedig a műszerdoboz áramellátása fontos
                    } elseif ($m->tipus == 'ZAJ') {
                        $result['aramforras'] = '220V';     // Arr::get($meres->doboz_adat, 'PWRSRCGRID', 0) == 1 ? '220V' : '12V';
                    }

                    // ha a legfrisseb mérés is 30 percnél régebbi, akkor pedig mondhatjuk, hogy nem működik
                    if ($meres->idopont < now()->subMinutes(30)) {
                        $result['aramforras'] = 'off';
                    }

                }
                return [$m->eazon => $result];

            })
        );
    }
}
