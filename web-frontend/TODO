
StationLister:
    ✔ átmutató nyilat a legközelebbi műszerhez programozni @done(19-10-19 09:02)
    ✔ digram és térkép gombokat vue-routerb<PERSON> be<PERSON><PERSON><PERSON> @done(19-10-19 09:02)
    ✔ NOx-nél a subscriptet Vue filterrel megoldani @done(19-10-19 09:02)
    ✔ console.error helyett valami felugró ablak kell @done(19-10-19 09:02)
    ✔ műszereket adatbázisból venni @done(19-11-20 17:59)
    ✔ aktuális állapot gombot tenni a műszerne<PERSON>k mellé, amire kattintva felugró layer-ben jönnek az adatok @done(19-12-13 18:29)
        (meteorológiánál a 30 percenkénti weboldal kártyáit kell átvenni, a zaj<PERSON>l ez alapján csinálni sajátot)
    ✔ met. html-t átdolgozni Vue komponensre zaj<PERSON><PERSON> hason<PERSON>an @done(21-09-09 14:17)
    ✔ energiaforrás státusza főoldalon lá<PERSON>zódjon @done(21-09-14 15:22)

Map-layout:
    status bar:
        ✔ komponenseket és színeket véglegesíteni @done(19-10-19 09:02)
    legend-drawer:
        ✔ property-n keresztül adatok fogadása a Map-layout-tól: színek, koncentrációk, mérőműszer feliratok @done(19-10-28 21:02)
        ✔ ahogy változik a kiválasztott időpont, zajnál a határérték is változhat @done(19-12-21 12:54)
        ✔ ahogy változik a kiválasztott időpont, a met. adatoknak változniuk kell @done(19-12-21 16:09)
        ✔ műszer fényképek jók legyenek @done(20-04-03 17:32)
    time-slider:
        ✔ 24 óránál nagyobb intervallumot ne lehessen kiválasztani @done(19-10-19 09:02)
        ✔ kipróbálni, hogy a második időpontválasztó helyett intervallum select legyen 1-24-óráig fél órás felbontással @done(19-10-19 09:01)
    map:
        ✔ térkép beizzítás @done(19-10-25 21:25)
        ✔ időpontnak megfelelő aktuális mérési eredményt nagyban is feltüntetni @done(19-10-28 16:59)
        ✔ mérési eredmények elérési út beszerzés műszer-ID és műszer-típus felhasználásával @done(20-04-03 21:41)
        ✔ mérési eredmények adatbázisból @done(20-04-06 21:32)
        ✔ számítási eredmények vizsgálati pontokra adatbázisból @done(20-04-06 21:32)
        ✔ fájlok elérési útjait év/hónap/nap könyvtárakra bontani @done(20-04-10 16:40)
        ✔ intervallumbontást 5 perces időközre állítani @done(20-04-10 16:40)
        ✔ műszer nem látszik? térkép közepe automatikusan műszerhez igazítva @done(21-09-09 19:21)

Diagram-layout:
    ✔ legend-drawer-ből másolatot készíteni @done(19-11-04 19:21)
    ✔ Apache echarts külön komponensbe @done(19-11-04 20:15)
    ✔ többfajta adatsort kiválaszthatóvá tenni diagdatadrawer-rel együttműködve @done(19-12-05 20:54)
    ✔ met. állomásnak külön diagramos megoldás kell: pl. polar szélkép sebességekkel -> echarts bar-polar-stack-radial? @done(19-12-20 23:07)
    ✔ met. állomás polár diagram alatt egyszerre kezelhető line diagramok: @done(19-12-18 16:54)
        hőmérséklet, légnyomás, besugárzás, páratartalom, eső
    ✔ met. állomás idősoros adatoknál légnyomás helyett szélirány kell @done(21-09-09 18:05)
    ✔ Harmonica Index implementáció a zajadatokhoz @done(22-07-27 15:03)

Admin-layout:
    ✔ login ablak @done(19-11-12 14:18)
    ✔ szerver adatok @done(19-11-13 15:00)
    ✔ felhasználók listája @done(19-11-13 15:00)
    ✔ merőállomások listázása bal oldalon és regisztrációja @done(19-11-13 22:24)
    ✔ logout gomb kell header jobb végébe @done(19-11-18 12:16)
    ✔ adminapi kommunikációt egységessé tenni @done(19-11-15 22:07)
    ✔ mérőállomás: alapadatok, tab panel megjelenítés: alapadat, specifikáció, állapotjellemzők, ssh terminál @done(19-11-28 17:24)
    ✔ mérőállomás: specifikáció lehetne q-tree-view-ban? @done(19-11-28 17:25)
    ✔ mérőállomás: doboz jellemzők diagramos megjelenítése @done(19-11-30 18:07)
    ✔ mérőállomás: js terminal emulator-ban ssh elérés a dobozhoz @done(19-11-19 12:02)
    ✔ telescope felületet elérhetővé tenni adminból @done(19-11-28 18:56)
    ✔ diagram kiegészítés met. adatokhoz @done(19-12-03 21:29)
    ✔ legutóbbi regisztrált IP címeket listázni az SSH terminál oldalon a zajmérőnél, többi műszernél a terminál nem is kell @done(20-01-27 15:45)
    ✔ admin token ne látszódjon már a logban!!! @done(20-01-27 17:27)
    ✔ Admin fényképet átállítani @done(21-07-12 16:25)
    ✔ műszer felvitelnél IMMON-tipus-hely-sorozatszam mezőket kelljen megadni ilyen sorrendben @done(21-07-12 17:44)
    ✔ új állapotjellemző: áramforrás hálózat vagy akkumulátor @done(21-09-13 18:40)

Adatbázis:
    ✔ szerkezet előkészítés @done(19-11-07 21:36)
    ✔ műszer specifikáció. adatok pótlása @done(19-11-23 13:04)

Backend:
    ✔ autentikációhoz admin rész JWT kialakítás @done(19-11-07 21:37)
    ✔ műszerek adatbázisba költöztetése @done(19-11-19 22:09)
    ✔ admin: mérőállomások adatai @done(19-11-13 22:26)
    ✔ adatfogadáshoz külön api és token auth csak műszereknek @done(19-11-21 13:57)
    ✔ mérési eredmények fogadása zajmérő felől adatbázisba: @done(19-11-22 18:31)
        token = 7gJzC9BUV5e3TIcMsOXxdaLtOjUD8UvtitPI605vLVj4kNVR21sjc3hAd9pLAeC0HSIP5NwYLG7cEpgG
    ✔ elmaradt mérési eredmények: valahogy be kell illeszteni a korábbi adatokba őket @done(19-12-03 16:56)
    ✔ IP cím regisztráció fogadása zajmérő felől adatbázisba @done(19-11-21 18:07)
    ✔ mérési eredmények fogadása met. állomástól adatbázisba: @done(19-12-03 11:22)
        token = 0L4XTQSpkNjY01oaPFWGqrJgIlGjtMh7sToTSZ4taP4xgi3rKlEb0VpQN5kvegLOYLJiiAV42Sw9lzMo
    ✔ IP cím helyett DNS név is lehet az elérési cím a muszerek táblában @done(19-11-20 21:42)
    ✔ UTC dátumok mellett, legyen helyi CET idő is tárolva az adatbázisban, hogy a megjelenítés gyors lehessen @done(20-01-27 20:39)
    ✔ kvmod eredménymentéshez API végpont @done(20-03-31 20:59)

Tesztek:
    ✔ API tesztek Postman alapján Newman-nel automatizálva? vagy sima laravel tesztek? @done(20-05-13 15:46)
    ✔ quasar frontend tesztek: jest unit tesztek -> majd ha jó lesz a vue 3 dependency a quasar testing kitben! @done(21-09-22 18:08)
    ✔ integrációs teszt: cypress.io @done(21-09-22 18:25)
    ✔ megjegyzésmezők alapján dokumentációgenerálás: vuese.org @done(20-04-21 16:52)

Deploy:
    ✔ imagweb-en domain-t beállítani @done(19-11-22 21:49)
    ✔ rendszer forrás szinkron, jogosultságok, adatbázis létrehozás @done(19-11-23 11:29)
    ✔ működöképessé tétel @done(19-11-23 18:45)
    ✔ DASPI-n adatfeltöltés címeit átállítani @done(19-11-23 18:45)
Quasar 2-re és Vue 3-ra frissítés:
    ✔ station object hiba a DiagramPage-nél @done(21-09-16 12:34)
    ✔ stationlister actdata csak akkor kell, ha az index az aktív oldal @done(21-09-15 20:37)
    ✔ echarts nagyon darabos és lassú, hosszab idejű nézetnél kevesebb, gyengébb felbontású adat kell? @done(21-09-17 22:05)
    ✔ qmap középpont a műszerhez legyen igazítva @done(21-09-17 13:31)
    ✔ térképen műszer adat igazítás: elég 1 tizedes és feljebb jobbra legyen a szám @done(21-09-17 15:06)
    ✔ echarts tree shaking-gel csökkenteni a bundle méretet: vendor.js 700 kB -> ? nem lehet sajnos @done(21-09-22 21:20)

2022-frissítés:
    ✔ geoip URL frissítés @done(22-06-27 12:15)
    ✔ DAVIS műszer integráció @done(22-06-28 18:14)
    ✔ zajmonitoring TELEKOM tandem SIM-mel (azzal sajnos nem megy, csak Vodafone-nal) @done(22-07-18 09:30)
    ✔ SPS30 pormérő integráció @done(22-07-22 18:24)
    ✔ pormérő adatmegjelenítés térképen és diagramon @done(22-07-25 18:33)
    ✔ met diagramon éjjeli időszakot átlátszóvá tenni @done(22-07-25 20:14)
    ✔ php 8 upgrade @done(22-11-17 18:49)
2023 javítások:
    ✔ admin menüpont nem választódik ki @done(23-02-02 17:08)
    ✔ pl. diagram link URL begépelésnél is működjön @done(23-02-02 15:14)
    ✔ soproni GPS és kép feltöltése @done(23-02-02 17:27)
    ✔ soproni vizsgálati pontra jellemző határértékek átírása @done(23-02-02 18:08)
    ✔ műszerek közül csak az aktuálisnak jelöltek látszódjanak @done(23-02-02 17:58)
    ✔ főoldalon átírni a szöveget: nyílall jelöljük a legközelebbit @done(23-02-02 17:41)
    ✔ műszer geojson-nek amikor a napi könyvtárak létrejönnek, nem jó a jogosultsága @done(23-02-06 16:47)
    ✔ idősoros diagramon az éjjel 22-06-ig legyen @done(23-02-06 16:18)
2023-fejlesztendő:
    - nappali és éjszakai határértékek a műszer adataiból veendőek
    - diagram oldal is tudjon folyamatosan frissülni
    - áramellátási forrás pontosabb meghatározása?
    - Harmonica Index érték ne legyen nagyobb 10-nél, vagy erre mi a szabály?
    - műszer SSH terminál oldalon a legutolsó ping-et is gyűjtse ki + gnome-terminál innen indítható közvetlenül?
    - python script restart kérés-nél megnézni, hogy van-e net, ha igen, akkor csak service restart kell, egyébként pedig reboot
    - python script-nél esetleg SSL ellenőrzést beállítani: https://urllib3.readthedocs.io/en/latest/user-guide.html#ssl
    - kell egy ProcessLevmeres job is, hogy az esetleges fájlrendszer hibák ne akasszák meg az Rpi monitoring-ot
    - loggolás Loguru-val talán jöbb lenne

2025 javítások:
    - quasar dev hiba: szabványos táblázatnál kell <tbody> tag mindenhol
    - quasar dev hiba: lighten is depreceated -> color.adjust($color, $lightness: 5%)?  doksi: https://sass-lang.com/d/color-functions
    - LARAVEL frissítés 11-re (12-re) kell-e most vagy később?
    ✔ kezdőoldal: ne minden mérőműszer látszódjon, csak ami aktívnak van jelölve -> új mező kell @done(25-08-20 17:12)
    - admin: külön oldal kell, ahol lehet állítani a jelenleg aktív műszereket
    - admin->új műszer: előző műszerből lehessen venni a legtöbb adatot és max átírni
